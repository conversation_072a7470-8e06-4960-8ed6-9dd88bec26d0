#!/bin/bash

# Test script for Cursor prefetch functionality
# This script helps debug and test the Cursor remote server prefetching

set -e

echo "🔍 Testing Cursor prefetch functionality..."
echo "=========================================="

# Check if we're in a Gitpod workspace
if [ -n "$GITPOD_WORKSPACE_ID" ]; then
    echo "✅ Running in Gitpod workspace: $GITPOD_WORKSPACE_ID"
else
    echo "⚠️  Not running in Gitpod workspace"
fi

# Check cache directory
CURSOR_CACHE_DIR="/workspace/.cursor-cache"
echo "📁 Checking cache directory: $CURSOR_CACHE_DIR"

if [ -d "$CURSOR_CACHE_DIR" ]; then
    echo "✅ Cache directory exists"
    ls -la "$CURSOR_CACHE_DIR"
else
    echo "❌ Cache directory does not exist"
    mkdir -p "$CURSOR_CACHE_DIR"
    echo "✅ Created cache directory"
fi

# Test Cursor URLs
echo ""
echo "🌐 Testing Cursor remote server URLs..."

CURSOR_URLS=(
    "https://downloads.cursor.sh/linux-x64/cursor-server"
    "https://downloads.cursor.sh/linux-x64/cursor-server-linux-x64"
)

for url in "${CURSOR_URLS[@]}"; do
    echo "Testing: $url"
    if curl --output /dev/null --silent --head --fail "$url"; then
        echo "✅ URL is accessible"
        
        # Try to download
        echo "📥 Attempting download..."
        if wget -O "$CURSOR_CACHE_DIR/cursor-server-test" "$url" 2>/dev/null; then
            echo "✅ Download successful"
            chmod +x "$CURSOR_CACHE_DIR/cursor-server-test" 2>/dev/null || true
            echo "📊 File size: $(du -h "$CURSOR_CACHE_DIR/cursor-server-test" | cut -f1)"
            echo "📋 File type: $(file "$CURSOR_CACHE_DIR/cursor-server-test" 2>/dev/null || echo "Unknown")"
        else
            echo "❌ Download failed"
        fi
    else
        echo "❌ URL is not accessible"
    fi
    echo ""
done

# Check system tools
echo "🛠️  Checking required tools..."
TOOLS=("wget" "curl" "tar" "openssh-client" "git" "node" "npm" "yarn" "pnpm")
for tool in "${TOOLS[@]}"; do
    if command -v "$tool" &> /dev/null; then
        echo "✅ $tool is available"
    else
        echo "❌ $tool is not available"
    fi
done

# Check environment variables
echo ""
echo "🔧 Checking environment variables..."
ENV_VARS=("NODE_ENV" "NPM_CONFIG_CACHE" "YARN_CACHE_FOLDER" "PNPM_STORE_DIR")
for var in "${ENV_VARS[@]}"; do
    if [ -n "${!var}" ]; then
        echo "✅ $var is set: ${!var}"
    else
        echo "❌ $var is not set"
    fi
done

# Check cache directories
echo ""
echo "📦 Checking cache directories..."
CACHE_DIRS=("/workspace/.npm-cache" "/workspace/.yarn-cache" "/workspace/.pnpm-store")
for dir in "${CACHE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir exists"
        echo "   Size: $(du -sh "$dir" 2>/dev/null | cut -f1 || echo "Unknown")"
    else
        echo "❌ $dir does not exist"
    fi
done

echo ""
echo "🎯 Test completed!"
echo ""
echo "📝 Notes:"
echo "- This test helps verify the Cursor prefetch setup"
echo "- The prefetched binary may not be used by Cursor due to version mismatches"
echo "- This is experimental and for debugging purposes only"
echo "- Check the documentation in docs/GITPOD_OPTIMIZATION.md for more details" 