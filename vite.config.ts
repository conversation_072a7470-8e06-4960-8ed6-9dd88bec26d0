import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { visualizer } from 'rollup-plugin-visualizer';
import compression from 'vite-plugin-compression';
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "0.0.0.0",
    port: 8080,
    headers: {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',

      'Cross-Origin-Embedder-Policy': 'unsafe-none',
      'Content-Security-Policy': `
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval' https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com data:;
        style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com;
        img-src 'self' data: https: blob:;
        font-src 'self' data: https://fonts.gstatic.com https://forms.fillout.com https://*.fillout.com;
        frame-src 'self' https://www.youtube.com https://www.google.com https://bitdefender.com https://*.bitdefender.com https://threatmap.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com;
        connect-src 'self' https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com;
        media-src 'self' https:;
        object-src 'none';
        base-uri 'self';
        form-action 'self' https://forms.fillout.com https://*.fillout.com;
        frame-ancestors 'none';
        upgrade-insecure-requests;
      `.replace(/\s+/g, ' ').trim(),
      'Permissions-Policy': 'camera=(self), microphone=(self), geolocation=(), interest-cohort=()',
    },
  },
  plugins: [
    react(),
    mode === 'production' && compression({
      algorithm: 'brotliCompress',
      ext: '.br',
    }),
    mode === 'production' && compression({
      algorithm: 'gzip',
      ext: '.gz',
    }),
    mode === 'analyze' && visualizer({
      open: true,
      filename: 'bundle-analyzer-report.html',
    }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    sourcemap: mode === 'development',
    target: 'es2020',
    minify: 'terser',
    cssMinify: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-tabs'],
          forms: ['react-hook-form', '@hookform/resolvers', 'zod'],
          motion: ['framer-motion'],
          utils: ['clsx', 'tailwind-merge', 'class-variance-authority'],
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          if (!assetInfo.name) {
            return 'assets/[ext]/[name]-[hash].[ext]';
          }
          
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `assets/images/[name]-[hash].${ext}`;
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
            return `assets/fonts/[name]-[hash].${ext}`;
          }
          return `assets/[ext]/[name]-[hash].${ext}`;
        },
      },
      external: (id: string) => {
        // Externalize large dependencies that can be loaded from CDN
        return ['lodash', 'moment'].includes(id);
      },
    },
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production',
        pure_funcs: mode === 'production' ? ['console.log', 'console.info'] : [],
      },
    },
  },
  base: '/',
}));
