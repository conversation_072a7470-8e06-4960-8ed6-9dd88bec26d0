{
	"name": "Node.js",
	"build": {
		"dockerfile": "Dockerfile"
	},
	"features": {
		"ghcr.io/devcontainers/features/common-utils:2": {
			"installZsh": "true",
			"username": "node",
			"upgradePackages": "true"
		},
		"ghcr.io/devcontainers/features/node:1": {
			"version": "none"
		},
		"ghcr.io/devcontainers/features/git:1": {
            "version": "latest",
            "ppa": "false"
        }
	},

	// Configure tool-specific properties.
	"customizations": {
		// Configure properties specific to VS Code.
		"vscode": {
			// Add the IDs of extensions you want installed when the container is created.
			"extensions": [
				"dbaeumer.vscode-eslint"
			]
		}
	},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "yarn install",

	// Set `remoteUser` to `root` to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "node"
}
