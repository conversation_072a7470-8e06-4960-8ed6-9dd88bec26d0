# Teclara Corp Component Reference

## Standard Page Components

### Hero Section Component
```jsx
import { motion } from 'framer-motion';
import LeadGenerationButton from '@/components/LeadGenerationButton';

const HeroSection = ({ title, subtitle, buttonText, formType, location }) => (
  <section className="pt-44 pb-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] relative overflow-hidden">
    {/* Red Glow Background Effect */}
    <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-red-500/30 rounded-full blur-3xl z-0" />
    
    {/* Background Grid Pattern (Optional) */}
    <div className="absolute inset-0 opacity-10">
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: `
            linear-gradient(rgba(52, 197, 182, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(52, 197, 182, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: "50px 50px",
        }}
      />
    </div>

    <div className="max-w-7xl mx-auto relative z-10">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
          {title}
        </h1>
        <p className="text-xl text-gray-300 max-w-4xl mx-auto font-medium leading-relaxed mb-8">
          {subtitle}
        </p>
        <div className="flex justify-center">
          <LeadGenerationButton
            formType={formType}
            location={location}
            buttonText={buttonText}
            className="bg-[#34c5b6] hover:bg-[#2ba89c] text-white border-none px-8 py-3 text-base font-semibold rounded-full transition-all duration-300 hover:shadow-[0_0_25px_rgba(254,196,0,0.5)]"
          />
        </div>
      </motion.div>
    </div>
  </section>
);
```

### Content Section Component
```jsx
import { motion } from 'framer-motion';

const ContentSection = ({ badge, title, subtitle, children }) => (
  <section className="py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
    <div className="max-w-7xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-20"
      >
        {badge && (
          <div className="inline-flex items-center gap-2 bg-[#34c5b6]/10 text-[#34c5b6] px-6 py-3 rounded-full text-sm font-medium mb-8 border border-[#34c5b6]/20">
            {badge}
          </div>
        )}
        <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
          {title}
        </h2>
        {subtitle && (
          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-medium">
            {subtitle}
          </p>
        )}
      </motion.div>
      
      {children}
    </div>
  </section>
);
```

### Service Card Component
```jsx
import { motion } from 'framer-motion';
import { CheckCircle } from 'lucide-react';

const ServiceCard = ({ icon: Icon, title, subtitle, description, details, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 30 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
    className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#34c5b6]/20 hover:border-[#34c5b6]/50 transition-all duration-300 hover:bg-[#060d25]/90"
  >
    <div className="bg-[#34c5b6]/10 p-4 rounded-xl inline-block mb-6">
      <Icon className="h-8 w-8 text-[#34c5b6]" />
    </div>
    <h3 className="text-2xl font-bold mb-2 tracking-wide">{title}</h3>
    {subtitle && (
      <p className="text-sm font-semibold text-[#FEC400] mb-4">{subtitle}</p>
    )}
    <p className="text-gray-300 mb-6 leading-relaxed font-medium">{description}</p>

    {details && (
      <ul className="space-y-3">
        {details.map((detail, idx) => (
          <li key={idx} className="flex items-start text-sm text-gray-300">
            <CheckCircle className="h-4 w-4 text-[#34c5b6] mr-3 flex-shrink-0 mt-0.5" />
            <span>{detail}</span>
          </li>
        ))}
      </ul>
    )}
  </motion.div>
);
```

### Feature Card Component (Simple)
```jsx
const FeatureCard = ({ icon: Icon, title, description }) => (
  <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#34c5b6]/20 hover:border-[#34c5b6]/40 transition-all duration-300">
    <Icon className="h-8 w-8 text-[#34c5b6] mb-4" />
    <h4 className="font-semibold mb-2 text-white uppercase tracking-wide text-sm">
      {title}
    </h4>
    <p className="text-gray-300 font-medium">
      {description}
    </p>
  </div>
);
```

### CTA Section Component
```jsx
import { motion } from 'framer-motion';
import LeadGenerationButton from '@/components/LeadGenerationButton';

const CTASection = ({ title, subtitle, buttonText, formType, location }) => (
  <section className="py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
    <div className="max-w-[1280px] mx-auto text-center">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="space-y-8"
      >
        <h2 className="text-3xl md:text-4xl font-bold text-white tracking-wide">
          {title}
        </h2>
        <p className="text-xl text-gray-300 leading-relaxed max-w-4xl mx-auto font-medium">
          {subtitle}
        </p>
        <LeadGenerationButton
          formType={formType}
          location={location}
          buttonText={buttonText}
          className="bg-[#34c5b6] hover:bg-[#2ba89c] text-white text-lg px-12 py-6 font-semibold transition-all duration-300 tracking-wide hover:shadow-[0_0_25px_rgba(254,196,0,0.5)] rounded-full"
        />
      </motion.div>
    </div>
  </section>
);
```

## Standard Layouts

### Standard Page Layout
```jsx
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Helmet } from 'react-helmet-async';
import CanonicalUrl from '@/components/CanonicalUrl';

const StandardPage = () => (
  <>
    <Helmet>
      {/* SEO Meta Tags */}
    </Helmet>
    <CanonicalUrl path="/page-path" />
    
    <div className="min-h-screen bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
      <Navbar />
      
      {/* Hero Section (Optional) */}
      <HeroSection />
      
      {/* Content Sections */}
      <ContentSection />
      
      {/* CTA Section */}
      <CTASection />
      
      <Footer />
    </div>
  </>
);
```

## Animation Utilities

### Standard Animation Props
```javascript
// Fade in from bottom
const fadeInUp = {
  initial: { opacity: 0, y: 30 },
  whileInView: { opacity: 1, y: 0 },
  viewport: { once: true },
  transition: { duration: 0.8 }
};

// Staggered animation for lists
const staggeredFadeIn = (index) => ({
  initial: { opacity: 0, y: 30 },
  whileInView: { opacity: 1, y: 0 },
  viewport: { once: true },
  transition: { duration: 0.6, delay: index * 0.1 }
});
```

## Utility Classes

### Common Class Combinations
```css
/* Section Container */
.section-container {
  @apply py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25];
}

/* Content Container */
.content-container {
  @apply max-w-7xl mx-auto;
}

/* Card Base */
.card-base {
  @apply bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#34c5b6]/20 hover:border-[#34c5b6]/40 transition-all duration-300;
}

/* Button Primary */
.btn-primary {
  @apply bg-[#34c5b6] hover:bg-[#2ba89c] text-white border-none px-8 py-3 text-base font-semibold rounded-full transition-all duration-300 hover:shadow-[0_0_25px_rgba(254,196,0,0.5)];
}

/* Title Large */
.title-large {
  @apply text-4xl md:text-6xl font-bold tracking-wide;
}

/* Title Medium */
.title-medium {
  @apply text-3xl md:text-4xl font-bold tracking-wide;
}

/* Body Text */
.body-text {
  @apply text-lg text-gray-300 leading-relaxed font-medium;
}
```

---

**Note**: This component reference should be used alongside the main design system documentation to ensure consistent implementation across all pages.
