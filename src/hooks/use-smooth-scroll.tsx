import { useEffect } from 'react';

interface SmoothScrollOptions {
  enabled?: boolean;
}

export const useSmoothScroll = (options: SmoothScrollOptions = {}) => {
  const { enabled = true } = options;

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    // Respect user's reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) return;

    // Simply ensure smooth scrolling is enabled on the document
    // Let CSS handle the actual smooth scrolling behavior
    document.documentElement.style.scrollBehavior = 'smooth';
    
    return () => {
      // Clean up on unmount
      document.documentElement.style.scrollBehavior = '';
    };
  }, [enabled]);
};

export default useSmoothScroll; 