import React from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import CanonicalUrl from '@/components/CanonicalUrl';

const Sitemap = () => {
  const mainPages = [
    { name: 'Home', path: '/' },
    { name: 'Solutions', path: '/solutions' },
    { name: 'Industries', path: '/industries' },
    { name: 'Threat Map', path: '/threatmap' },
    { name: 'Pricing', path: '/pricing' },
    { name: 'About', path: '/about' },
    { name: 'Contact', path: '/contact' },
  ];

  const landingPages = [
    { name: 'Cybersecurity', path: '/cybersecurity' },
    { name: 'Managed IT', path: '/managed-it' },
  ];

  const caseStudies = [
    { name: 'Law Firm Case Study', path: '/case-studies/law-firm' },
    { name: 'Financial Services Case Study', path: '/case-studies/financial-services' },
    { name: 'Consulting Firm Case Study', path: '/case-studies/consulting' },
    { name: 'Architecture Firm Case Study', path: '/case-studies/architecture' },
    { name: 'Startup Case Study', path: '/case-studies/startup' },
  ];

  return (
    <>
      <Helmet>
        <title>Sitemap | Teclara Technologies</title>
        <meta name="description" content="Complete sitemap of Teclara Technologies website. Navigate to all cybersecurity services, IT solutions, case studies, pricing, and contact information pages." />
        <meta name="robots" content="noindex, follow" />
      </Helmet>
      <CanonicalUrl path="/sitemap" />
      
      <div className="min-h-screen flex flex-col bg-white">
        <Navbar />
        
        <main className="flex-grow py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-gray-900 mb-8">Sitemap</h1>
            
            {/* Main Pages */}
            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Main Pages</h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {mainPages.map((page) => (
                  <li key={page.path}>
                    <Link 
                      to={page.path}
                      className="text-teclara-primary hover:text-teclara-primary-dark transition-colors"
                    >
                      {page.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </section>

            {/* Landing Pages */}
            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Service Pages</h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {landingPages.map((page) => (
                  <li key={page.path}>
                    <Link 
                      to={page.path}
                      className="text-teclara-primary hover:text-teclara-primary-dark transition-colors"
                    >
                      {page.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </section>

            {/* Case Studies */}
            <section className="mb-12">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Case Studies</h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {caseStudies.map((page) => (
                  <li key={page.path}>
                    <Link 
                      to={page.path}
                      className="text-teclara-primary hover:text-teclara-primary-dark transition-colors"
                    >
                      {page.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </section>
          </div>
        </main>

        <Footer />
      </div>
    </>
  );
};

export default Sitemap; 