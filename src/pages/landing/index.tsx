import React, { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useInView, useAnimation } from 'framer-motion';
import { 
  Shield, 
  Activity, 
  FileCheck, 
  Users, 
  Clock, 
  Lock, 
  AlertCircle, 
  CheckCircle,
  ArrowRight,
  Send,
  Facebook,
  Linkedin,
  Instagram,
  Twitter
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';
import PartnerLogo from '@/components/PartnerLogo';
import { Helmet } from 'react-helmet-async';
import CanonicalUrl from '@/components/CanonicalUrl';
import LeadGenerationButton from '@/components/LeadGenerationButton';
import { FilloutStandardEmbed } from '@fillout/react';

// Form schema for the contact form
const contactFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  company: z.string().min(2, { message: "Company name must be at least 2 characters." }),
  message: z.string().min(10, { message: "Message must be at least 10 characters." }),
});

// Animation variants for staggered animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5, ease: "easeOut" }
  },
};

// Services data
const services = [
  {
    icon: <Shield className="h-12 w-12 text-blue-500" />,
    title: "Managed Detection & Response (MDR)",
    description: "Safeguard your business from breaches with 24/7 threat detection, containment, and response—powered by SentinelOne and Huntress and operated by Teclara's expert SOC. We stop threats before they disrupt your operations."
  },
  {
    icon: <Activity className="h-12 w-12 text-green-500" />,
    title: "Patch Management & Hardening",
    description: "Close security gaps before they're exploited. Our automated patching and system hardening framework ensures your environment is always up to date and compliant—minimizing downtime and vulnerability exposure."
  },
  {
    icon: <AlertCircle className="h-12 w-12 text-red-500" />,
    title: "Email Security & Awareness",
    description: "Eliminate the #1 threat vector—your inbox. We combine AI-driven email protection from Ironscales with ongoing user training and phishing simulations to reduce employee-driven risk across your organization."
  },
  {
    icon: <FileCheck className="h-12 w-12 text-yellow-500" />,
    title: "Backup & Disaster Recovery",
    description: "Ensure business continuity with automated, compliance-ready backups of Microsoft 365 and Google Workspace—powered by Dropsuite. Includes recovery validation and retention policies aligned to your legal and regulatory obligations."
  }
];

// Value propositions data
const valueProps = [
  {
    icon: <Users className="h-10 w-10 text-teclara-primary" />,
    title: "Security-First Culture",
    description: "Every Teclara engagement starts with cybersecurity. We don't bolt it on—we build it in, from identity to infrastructure."
  },
  {
    icon: <Clock className="h-10 w-10 text-teclara-primary" />,
    title: "Fast, Guaranteed SLAs",
    description: "Under 15-minute response for critical issues, and proactive resolution before users even notice."
  },
  {
    icon: <Lock className="h-10 w-10 text-teclara-primary" />,
    title: "Unified IT & Security Stack",
    description: "All-in-one flat-rate offering covering endpoints, email, backup, vulnerability management, and SIEM—no nickel-and-diming."
  }
];

// Testimonials data
const testimonials = [
  {
    quote: "Implementing their security solution has been transformative for our organization. We now have complete visibility into our security posture.",
    author: "Sarah Johnson",
    title: "CIO, Enterprise Financial",
    logo: "/placeholder.svg"
  },
  {
    quote: "Their team's response to our security incident was nothing short of extraordinary. They contained the threat within minutes.",
    author: "Michael Chen",
    title: "Director of IT, TechNova Corp",
    logo: "/placeholder.svg"
  },
  {
    quote: "The ongoing security monitoring gives us peace of mind. Their monthly reports help us continuously improve our security posture.",
    author: "Amanda Rodriguez",
    title: "CISO, Healthcare Solutions",
    logo: "/placeholder.svg"
  }
];

const Landing = () => {
  const { toast } = useToast();
  const buildNumber = import.meta.env.VITE_BUILD_NUMBER 
    ? import.meta.env.VITE_BUILD_NUMBER.substring(0, 7)
    : 'local';
  const servicesRef = useRef(null);
  const valuePropsRef = useRef(null);
  const testimonialsRef = useRef(null);
  const contactRef = useRef(null);
  
  const servicesInView = useInView(servicesRef, { once: true, amount: 0.3 });
  const valuePropsInView = useInView(valuePropsRef, { once: true, amount: 0.3 });
  const testimonialsInView = useInView(testimonialsRef, { once: true, amount: 0.3 });
  
  const servicesControls = useAnimation();
  const valuePropsControls = useAnimation();
  const testimonialsControls = useAnimation();
  
  useEffect(() => {
    if (servicesInView) servicesControls.start("visible");
    if (valuePropsInView) valuePropsControls.start("visible");
    if (testimonialsInView) testimonialsControls.start("visible");
  }, [servicesInView, valuePropsInView, testimonialsInView, servicesControls, valuePropsControls, testimonialsControls]);

  // Form handling
  const form = useForm<z.infer<typeof contactFormSchema>>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      message: ""
    },
  });

  const onSubmit = (values: z.infer<typeof contactFormSchema>) => {
    // We're just displaying a toast for now as per requirements
    // Form submission logging removed for production
    toast({
      title: "Message received",
      description: "Thank you for reaching out. We'll be in touch shortly.",
    });
    form.reset();
  };

  const scrollToContact = () => {
    contactRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <>
      <Helmet>
        <title>Enterprise Security & IT Services for Oakville, Hamilton, Milton, St. Catharines & Halton Region | Teclara Technologies</title>
        <meta name="description" content="Enterprise security and IT services with flat-rate pricing: comprehensive cybersecurity, managed IT support, business continuity, and 24/7 monitoring for complete business protection." />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Teclara Technologies",
            "url": "https://teclara.tech",
            "description": "Enterprise-grade security and IT services for modern businesses, including threat detection, monitoring, and expert response.",
            "areaServed": "Canada",
            "serviceType": "IT Services & Security"
          })}
        </script>
      </Helmet>
      <CanonicalUrl path="/" />
      
      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="relative py-10 md:py-16 overflow-hidden bg-gradient-to-br from-[#1F252F] to-[#2563eb] text-white flex flex-col items-center justify-center text-center">
          {/* Main Content Container - Centered */}
          <div className="container mx-auto px-4 flex flex-col items-center justify-center">
            {/* Logo */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex justify-center mb-6"
            >
              <Link to="/">
                <img 
                  src="/uploads/teclara_logo_white_text.png" 
                  alt="Teclara" 
                  className="h-12 w-auto"
                />
              </Link>
            </motion.div>
            {/* Headline */}
            <h1 className="text-5xl md:text-7xl font-extrabold mb-6 leading-tight">
              Enterprise <span className="bg-gradient-to-r from-blue-400 to-teal-400 text-transparent bg-clip-text">Cybersecurity</span><br />Made Simple
            </h1>
            {/* Subheading */}
            <p className="text-lg md:text-2xl text-gray-200 mb-10 max-w-2xl font-medium">
              Protect your business with 24/7 managed security services. Advanced threat detection, rapid incident response, and complete compliance assistance.
            </p>
            
            {/* Stats Row */}
            <div className="flex flex-wrap justify-center gap-6 w-full max-w-lg mb-4">
              <motion.div
                whileHover={{ scale: 1.05 }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="flex flex-col items-center p-2 cursor-pointer transition-all min-w-[100px]"
              >
                <Shield className="h-6 w-6 text-blue-400 mb-1" />
                <span className="text-xl md:text-2xl font-extrabold text-white drop-shadow">24/7</span>
                <span className="text-xs md:text-sm text-gray-200 text-center font-medium">Monitoring</span>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex flex-col items-center p-2 cursor-pointer transition-all min-w-[100px]"
              >
                <AlertCircle className="h-6 w-6 text-green-400 mb-1" />
                <span className="text-xl md:text-2xl font-extrabold text-white drop-shadow">99%+</span>
                <span className="text-xs md:text-sm text-gray-200 text-center font-medium">Phishing Detection</span>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="flex flex-col items-center p-2 cursor-pointer transition-all min-w-[100px]"
              >
                <FileCheck className="h-6 w-6 text-blue-500 mb-1" />
                <span className="text-xl md:text-2xl font-extrabold text-white drop-shadow">100%</span>
                <span className="text-xs md:text-sm text-gray-200 text-center font-medium">Client Data Backed Up Daily</span>
              </motion.div>
            </div>
            <p className="text-xs text-gray-300 max-w-md leading-tight text-center mb-8">
              Based on vendor-reported capabilities under standard deployment conditions. Teclara configures and monitors these tools for optimal performance.
            </p>

            {/* CTA Row - Moved here */}
            <div className="flex flex-col items-center justify-center space-y-6">
              <h3 className="text-2xl font-bold text-white">Ready to Get Started?</h3>
              <p className="text-gray-300 text-center max-w-md">
                Take the first step towards securing your business. Get your free risk assessment today.
              </p>
              <LeadGenerationButton
                formType="IT_CONSULT"
                location="Landing Page"
              />
            </div>
          </div>
          <p className="text-xs text-gray-300 mt-2 text-center max-w-xl mx-auto">Providing your email will allow us to get in touch to discuss your needs and is in accordance with our <a href="/privacy-policy" className="underline hover:text-white">privacy policy</a> and <a href="/terms-of-service" className="underline hover:text-white">terms of service</a>.</p>
        </section>

        {/* Services Section */}
        <section ref={servicesRef} className="py-24 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <motion.h2 
                className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={servicesInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5 }}
              >
                Comprehensive Security Services
              </motion.h2>
              <motion.p 
                className="text-xl text-gray-600 max-w-3xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={servicesInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                End-to-end protection for your critical assets, data, and infrastructure.
              </motion.p>
            </div>
            
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
              variants={containerVariants}
              initial="hidden"
              animate={servicesControls}
            >
              {services.map((service, index) => (
                <motion.div 
                  key={index} 
                  className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                  variants={itemVariants}
                >
                  <div className="bg-gray-50 p-4 inline-block rounded-lg mb-4">
                    {service.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section ref={testimonialsRef} className="py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Why Partner With Teclara?
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                We become an extension of your business, aligning IT strategy and cybersecurity to protect your operations and drive growth.
              </p>
            </div>

            {/* Metrics Cards */}
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {/* Card 1: Security-First Culture */}
              <div className="bg-white/10 backdrop-blur-sm border-white/20 text-center group hover:bg-white/20 transition-all duration-300 rounded-2xl">
                <div className="p-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-teal-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    {/* Checkmark-in-circle icon */}
                    <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path d="M9 12l2 2l4 -4" />
                      <circle cx="12" cy="12" r="10" />
                    </svg>
                  </div>
                  <div className="text-3xl font-bold text-blue-400 mb-2">Built-In</div>
                  <div className="text-sm text-gray-300 mb-3">By Design</div>
                  <h3 className="text-lg font-semibold text-white mb-2">Security-First Culture</h3>
                  <p className="text-gray-300 text-sm">Cybersecurity is integrated into every layer of our service delivery, not treated as an add-on.</p>
                </div>
              </div>
              {/* Card 2: Fast, Guaranteed SLAs */}
              <div className="bg-white/10 backdrop-blur-sm border-white/20 text-center group hover:bg-white/20 transition-all duration-300 rounded-2xl">
                <div className="p-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-teal-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    {/* Clock icon */}
                    <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <circle cx="12" cy="12" r="10" />
                      <path d="M12 6v6l4 2" />
                    </svg>
                  </div>
                  <div className="text-3xl font-bold text-blue-400 mb-2">15min</div>
                  <div className="text-sm text-gray-300 mb-3">Critical Response</div>
                  <h3 className="text-lg font-semibold text-white mb-2">Fast, Guaranteed SLAs</h3>
                  <p className="text-gray-300 text-sm">We respond to critical issues within 15 minutes—faster than most internal teams.</p>
                </div>
              </div>
              {/* Card 3: Unified IT & Security Stack */}
              <div className="bg-white/10 backdrop-blur-sm border-white/20 text-center group hover:bg-white/20 transition-all duration-300 rounded-2xl">
                <div className="p-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-teal-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    {/* Stacked-circle play-like/shield icon */}
                    <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      {/* Shield icon */}
                      <path d="M12 3l7 4v5c0 5.25-3.5 10-7 10s-7-4.75-7-10V7z" />
                    </svg>
                  </div>
                  <div className="text-3xl font-bold text-blue-400 mb-2">All-in-One</div>
                  <div className="text-sm text-gray-300 mb-3">Flat-Rate</div>
                  <h3 className="text-lg font-semibold text-white mb-2">Unified IT & Security Stack</h3>
                  <p className="text-gray-300 text-sm">One plan, one price, no surprises. Complete coverage from endpoint to cloud.</p>
                </div>
              </div>
              {/* Card 4: Business-Aligned IT */}
              <div className="bg-white/10 backdrop-blur-sm border-white/20 text-center group hover:bg-white/20 transition-all duration-300 rounded-2xl">
                <div className="p-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-teal-400 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    {/* Checkmark-in-circle icon */}
                    <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path d="M9 12l2 2l4 -4" />
                      <circle cx="12" cy="12" r="10" />
                    </svg>
                  </div>
                  <div className="text-3xl font-bold text-blue-400 mb-2">Strategic</div>
                  <div className="text-sm text-gray-300 mb-3">Partnership</div>
                  <h3 className="text-lg font-semibold text-white mb-2">Business-Aligned IT</h3>
                  <p className="text-gray-300 text-sm">We align IT and security to your business goals—not the other way around.</p>
                </div>
              </div>
            </div>

            {/* Benefits Grid */}
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-bold text-white mb-6">
                  Complete Security Coverage
                </h3>
                <p className="text-gray-300 mb-8 text-lg leading-relaxed">
                  Our comprehensive approach ensures every aspect of your cybersecurity is covered. From preventive measures to incident response, we provide end-to-end protection that scales with your business.
                </p>
                <div className="grid gap-4">
                  {["24/7/365 Security Operations Center","Advanced AI and Machine Learning","Regulatory Compliance Expertise","Scalable Security Solutions","Cost-Effective Protection","Proactive Threat Hunting"].map((benefit, index) => (
                    <div key={index} className="flex items-center">
                      <svg className="h-6 w-6 text-green-400 mr-4 flex-shrink-0" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M9 12l2 2l4 -4" /><circle cx="12" cy="12" r="10" /></svg>
                      <span className="text-gray-300 text-lg">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-blue-600/20 to-teal-600/20 backdrop-blur-sm border border-white/10 rounded-3xl p-8 text-center">
                  <div className="text-6xl font-bold text-blue-400 mb-4">99%+</div>
                  <div className="text-xl text-white mb-2">Phishing/BEC Detection Accuracy</div>
                  <div className="text-gray-300 mb-6">Industry-leading detection of phishing and business email compromise threats</div>
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-teal-400">25+</div>
                      <div className="text-gray-300 text-sm">Years Industry Experience</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-400">24/7</div>
                      <div className="text-gray-300 text-sm">Monitoring</div>
                    </div>
                  </div>
                </div>
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-blue-400 rounded-full animate-pulse"></div>
                <div className="absolute -bottom-6 -left-6 w-12 h-12 bg-teal-400 rounded-full opacity-60 animate-pulse delay-300"></div>
              </div>
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section ref={valuePropsRef} className="py-24 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center mb-16">
              <motion.h2 
                className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={valuePropsInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5 }}
              >
                Why Choose Our Security Services
              </motion.h2>
              <motion.p 
                className="text-xl text-gray-600"
                initial={{ opacity: 0, y: 20 }}
                animate={valuePropsInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                We deliver more than security solutions—we provide peace of mind.
              </motion.p>
            </div>
            
            <motion.div 
              className="grid grid-cols-1 lg:grid-cols-3 gap-10 max-w-5xl mx-auto"
              variants={containerVariants}
              initial="hidden"
              animate={valuePropsControls}
            >
              {valueProps.map((prop, index) => (
                <motion.div 
                  key={index} 
                  className="flex flex-col items-center text-center bg-white p-6 rounded-xl shadow-sm"
                  variants={itemVariants}
                >
                  <div className="mb-4 p-3 bg-teclara-primary/10 rounded-full">
                    {prop.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{prop.title}</h3>
                  <p className="text-gray-600">{prop.description}</p>
                </motion.div>
              ))}
            </motion.div>
            
            {/* Additional USPs in a grid */}
            <div className="mt-20 max-w-5xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[
                  "Advanced AI-powered threat detection",
                  "Fully managed Cloud Environment", 
                  "Cloud and on-premise security expertise",
                  "Customized security architecture",
                  "Regular penetration testing and audits", 
                  "Transparent reporting and dashboards"
                ].map((item, index) => (
                  <motion.div 
                    key={index}
                    className="flex items-center p-4 bg-white rounded-lg shadow-sm border border-gray-100"
                    initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                    animate={valuePropsInView ? { opacity: 1, x: 0 } : {}}
                    transition={{ duration: 0.5, delay: 0.3 + (index * 0.1) }}
                  >
                    <CheckCircle className="h-5 w-5 text-teclara-primary mr-3 flex-shrink-0" />
                    <span className="text-gray-700">{item}</span>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section - Ready to Secure Your Business */}
        <section className="bg-[#0A0A0A] border-t border-[#333] py-16 text-white">
          <div className="container mx-auto px-4 max-w-7xl">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
              {/* Left: Info & Contact */}
              <div>
                <h2 className="text-3xl md:text-4xl font-extrabold mb-4 text-white">Ready to Secure Your Business?</h2>
                <p className="text-lg text-gray-300 mb-8">Get started with a free security assessment. Our experts will analyze your current security posture and provide personalized recommendations.</p>
                <div className="mb-8">
                  <h3 className="text-xl font-bold mb-2 text-white">Get in Touch</h3>
                  <p className="mb-2 text-gray-300">Schedule a consultation with our cybersecurity experts. We'll discuss your specific needs and show you how Teclara Technologies can protect your organization.</p>
                  <div className="space-y-1 text-base">
                    <div>
                      <span className="font-semibold text-white">Email Us</span>
                      <br />
                      <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors"><EMAIL></a>
                    </div>
                    <div className="mt-2">
                      <span className="font-semibold text-white">Call Us</span>
                      <br />
                      <a href="tel:+13659965856" className="text-blue-400 hover:text-blue-300 transition-colors">(*************</a>
                    </div>
                  </div>
                </div>
                <div className="mb-8">
                  <h3 className="text-lg font-bold mb-2 text-white">Why Start With Us?</h3>
                  <ul className="list-disc list-inside text-gray-300 space-y-1">
                    <li>Free security assessment (no obligation)</li>
                    <li>30-minute expert consultation</li>
                    <li>Custom security roadmap</li>
                    <li>Implementation timeline & pricing</li>
                  </ul>
                </div>
              </div>
              {/* Right: Contact Form */}
              <div>
                <div className="bg-[#181C23] rounded-2xl p-8 shadow-lg border border-gray-700">
                  <h3 className="text-2xl font-bold mb-6 text-white">Request Your Free Assessment</h3>
                  <div className="w-full" style={{ height: '600px' }}>
                    <FilloutStandardEmbed
                      filloutId="38CVFXhpBfus"
                      inheritParameters
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      {/* Minimal Footer */}
      <footer className="bg-[#0A0A0A] border-t border-[#333] py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center space-y-4">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="flex justify-center mb-6"
              >
                <Link to="/">
                  <img 
                    src="/uploads/teclara_logo_white_text.png" 
                    alt="Teclara" 
                    className="h-12 w-auto"
                  />
                </Link>
              </motion.div>
            </div>

            {/* Copyright */}
            <p className="text-white/70 text-sm">
              © 2025 Teclara Technologies Inc. | <a href="/privacy-policy" className="hover:text-white transition-colors">Privacy Policy</a> | <a href="/terms-of-service" className="hover:text-white transition-colors">Terms</a> | <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">Contact</a>
            </p>
            <p className="text-white/70 text-sm">
              Build: <span className="italic">{buildNumber}</span>
            </p>
          </div>
        </div>
      </footer>
    </>
  );
};

export default Landing;
