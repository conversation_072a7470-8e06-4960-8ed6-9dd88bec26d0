import React from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Activity, 
  FileCheck, 
  Users, 
  Clock, 
  Lock, 
  AlertCircle, 
  CheckCircle, 
  ArrowRight, 
  Send,
  Monitor,
  Cloud,
  GraduationCap,
  Server,
  Wrench,
  Zap,
  Settings,
  UserCog,
  Mail,
  Globe,
  DollarSign
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';
import PartnerLogo from '@/components/PartnerLogo';
import { Helmet } from 'react-helmet-async';
import CanonicalUrl from '@/components/CanonicalUrl';
import { ButtonLink } from '@/components/ui/button-link';
import { 
  createLocalBusinessSchema,
  createServiceSchema,
  createFAQSchema 
} from '@/utils/schemaData';
import LeadGenerationButton from '@/components/LeadGenerationButton';
import { FilloutStandardEmbed } from '@fillout/react';

// Form schema for the contact form
const contactFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  company: z.string().min(1, { message: "Company name is required." }),
  phone: z.string().min(10, { message: "Please enter a valid phone number." }),
});

const ManagedITLanding = () => {
  const { toast } = useToast();
  const buildNumber = import.meta.env.VITE_BUILD_NUMBER 
    ? import.meta.env.VITE_BUILD_NUMBER.substring(0, 7)
    : 'local';
  
  // Form handling
  const form = useForm<z.infer<typeof contactFormSchema>>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      company: "",
      phone: ""
    },
  });

  // Removed form abandon tracking - focusing on conversion events only

  // Create FAQ data
  const managedITFAQs = [
    {
      question: "What is included in your managed IT services?",
      answer: "Our managed IT services include 24/7 monitoring, help desk support, proactive maintenance, cloud solutions, network management, and strategic IT planning, all delivered through a flat-rate pricing model."
    },
    {
      question: "How quickly do you respond to IT issues?",
      answer: "We provide rapid response times with our 24/7 help desk. Critical issues are addressed immediately, while non-critical issues are handled within our agreed service level agreements."
    },
    {
      question: "Do you offer cloud services?",
      answer: "Yes, we provide comprehensive cloud solutions including Microsoft 365, cloud backup, and secure cloud storage, helping businesses modernize their IT infrastructure while maintaining security."
    },
    {
      question: "How do you handle IT emergencies?",
      answer: "Our 24/7 support team is always available for emergencies. We have established protocols for rapid response, including remote and on-site support, to minimize downtime and ensure business continuity."
    }
  ];

  // Create schema data
  const localBusinessSchema = createLocalBusinessSchema({
    name: "Teclara Technologies",
    description: "Comprehensive managed IT services for Canadian SMBs",
    address: {
      street: "123 Business Street",
      city: "Oakville",
      region: "ON",
      postalCode: "L6H 0C3",
      country: "CA"
    },
    geo: {
      latitude: 43.4675,
      longitude: -79.6877
    },
    phone: "******-123-4567",
    email: "<EMAIL>",
    priceRange: "$$",
    openingHours: [
      "Monday 09:00-17:00",
      "Tuesday 09:00-17:00",
      "Wednesday 09:00-17:00",
      "Thursday 09:00-17:00",
      "Friday 09:00-17:00"
    ]
  });

  const serviceSchema = createServiceSchema(
    "Managed IT Services",
    "Complete managed IT services for Ontario businesses, including unlimited support, proactive monitoring, and cloud workspace management.",
    "IT Management",
    {
      areaServed: ["Oakville", "Hamilton", "Milton", "St. Catharines", "Halton Region"],
      serviceOutput: [
        "24/7 Help Desk",
        "Proactive Monitoring",
        "Cloud Solutions",
        "Network Management",
        "Strategic Planning",
        "Hardware Support",
        "Software Management",
        "Data Backup",
        "Security Management",
        "Staff Training"
      ]
    }
  );

  const faqSchema = createFAQSchema(managedITFAQs);

  return (
    <>
      <Helmet>
        <title>Managed IT Services for Oakville, Hamilton, Milton, St. Catharines & Halton Region | Teclara Technologies</title>
        <meta name="description" content="Comprehensive managed IT services: proactive monitoring, help desk support, cloud management, and strategic IT planning. Flat-rate pricing with no hidden fees for predictable budgeting." />
        
        {/* Page specific Open Graph */}
        <meta property="og:title" content="Managed IT Services | Teclara Technologies" />
        <meta property="og:description" content="Transform your IT operations with proactive monitoring, 24/7 support, and cloud solutions from local experts." />
        <meta property="og:url" content="https://teclara.tech/managed-it" />
        <meta property="og:type" content="website" />
        <meta property="og:image" content="https://teclara.tech/uploads/teclara_logo_black_text.png" />
        <meta property="og:image:alt" content="Teclara Technologies Managed IT Services" />
        
        {/* Page specific Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Managed IT Services | Teclara Technologies" />
        <meta name="twitter:description" content="Transform your IT operations with our comprehensive managed services. 24/7 support, proactive monitoring, and cloud solutions from local experts." />
        <meta name="twitter:image" content="https://teclara.tech/uploads/teclara_logo_black_text.png" />
        
        {/* Additional meta tags */}
        <meta name="keywords" content="managed IT services, IT support, help desk, cloud solutions, network management, IT consulting, Oakville, Hamilton, Milton, St. Catharines, Halton Region" />
        <meta name="author" content="Teclara Technologies" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="en-CA" />
        <meta name="geo.region" content="CA-ON" />
        <meta name="geo.placename" content="Oakville, Ontario" />
        <meta name="geo.position" content="43.4675;-79.6877" />
        <meta name="ICBM" content="43.4675, -79.6877" />
        
        {/* Resource hints */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Schema.org data */}
        <script type="application/ld+json">
          {JSON.stringify(localBusinessSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(serviceSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(faqSchema)}
        </script>
      </Helmet>
      <CanonicalUrl path="/managed-it" />
      
      <main className="min-h-screen">
        {/* Logo Section */}
        <div className="py-8 bg-[#1F252F]">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex justify-center"
            >
              <Link to="/">
                <img 
                  src="/uploads/teclara_logo_white_text.png" 
                  alt="Teclara Logo" 
                  className="h-12 w-auto"
                />
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Hero Section */}
        <section className="relative py-24 md:py-32 overflow-hidden bg-gradient-to-b from-[#1F252F] to-[#2A2A2A] text-white">
          <div className="absolute inset-0 bg-grid-white/[0.05] bg-[length:20px_20px]" aria-hidden="true" />
          
          {/* Animated background elements */}
          <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-teclara-primary/20 rounded-full blur-3xl opacity-20" aria-hidden="true" />
          <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-teal-400/20 rounded-full blur-3xl opacity-20" aria-hidden="true" />
          
          <div className="container mx-auto px-4 relative z-10">
            <motion.div 
              className="max-w-4xl mx-auto text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-tight">
                Worry-Free IT Operations—<span className="text-teclara-primary">Fully Managed by Experts</span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 mb-10 max-w-3xl mx-auto">
                Focus on growing your business while we handle your IT. Get unlimited support, proactive monitoring, and complete cloud workspace management.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <LeadGenerationButton
                  formType="IT_CONSULT"
                  location="Managed IT Landing Hero"
                />
              </div>
            </motion.div>
          </div>
        </section>

        {/* Key Benefits Section */}
        <section className="relative py-24 overflow-hidden">
          {/* Animated Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-teal-50">
            {/* Animated circles */}
            <motion.div
              className="absolute top-1/4 left-1/4 w-64 h-64 bg-teclara-primary/5 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                x: [0, 30, 0],
                y: [0, -30, 0],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.3, 1],
                x: [0, -40, 0],
                y: [0, 40, 0],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            {/* Grid Pattern */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAgMTBMMCAwTTEwIDEwTDIwIDBNMTAgMTBMMCAyME0xMCAxMEwyMCAyMCIgc3Ryb2tlPSIjMDAwIiBzdHJva2Utb3BhY2l0eT0iMC4wMiIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9zdmc+')] bg-[length:20px_20px]" />
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <motion.div 
              className="text-center mb-16"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Support That Solves Problems—Before They Disrupt Business
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                We don't wait for things to break. Our team monitors, manages, and improves your IT infrastructure—so your team stays productive and secure.
              </p>
            </motion.div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {[
                {
                  icon: <Wrench className="h-12 w-12 text-blue-400" />,
                  title: "Unlimited Help Desk + Priority Ticket Response",
                  description: "Get immediate support when you need it, with guaranteed response times for critical issues.",
                  color: "from-blue-50 to-blue-100/50",
                  iconBg: "bg-blue-100",
                  iconColor: "text-blue-600",
                  delay: 0.1
                },
                {
                  icon: <Zap className="h-12 w-12 text-purple-400" />,
                  title: "Proactive Patching + System Monitoring",
                  description: "We monitor your systems 24/7 and keep everything up-to-date to prevent issues before they occur.",
                  color: "from-purple-50 to-purple-100/50",
                  iconBg: "bg-purple-100",
                  iconColor: "text-purple-600",
                  delay: 0.2
                },
                {
                  icon: <Cloud className="h-12 w-12 text-green-400" />,
                  title: "Microsoft 365 & Google Workspace Admin",
                  description: "Complete management of your cloud workspace, including email, storage, and collaboration tools.",
                  color: "from-green-50 to-green-100/50",
                  iconBg: "bg-green-100",
                  iconColor: "text-green-600",
                  delay: 0.3
                },
                {
                  icon: <UserCog className="h-12 w-12 text-cyan-400" />,
                  title: "New User Setup + Device Lifecycle Management",
                  description: "Streamlined onboarding and offboarding processes for users and devices.",
                  color: "from-cyan-50 to-cyan-100/50",
                  iconBg: "bg-cyan-100",
                  iconColor: "text-cyan-600",
                  delay: 0.4
                },
                {
                  icon: <Shield className="h-12 w-12 text-red-400" />,
                  title: "Endpoint Hardening + Policy Enforcement",
                  description: "Secure your devices and enforce security policies across your organization.",
                  color: "from-red-50 to-red-100/50",
                  iconBg: "bg-red-100",
                  iconColor: "text-red-600",
                  delay: 0.5
                },
                {
                  icon: <Settings className="h-12 w-12 text-emerald-400" />,
                  title: "Strategic IT Guidance & Quarterly Reviews",
                  description: "Regular strategy sessions to align IT with your business goals and growth.",
                  color: "from-emerald-50 to-emerald-100/50",
                  iconBg: "bg-emerald-100",
                  iconColor: "text-emerald-600",
                  delay: 0.6
                }
              ].map((feature, index) => (
                <motion.div 
                  key={index}
                  className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${feature.color} p-8 shadow-lg border border-gray-100/50 hover:shadow-xl transition-all duration-300`}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: feature.delay }}
                  whileHover={{ y: -5 }}
                >
                  {/* Animated Background Elements */}
                  <motion.div
                    className="absolute -right-4 -bottom-4 w-24 h-24 bg-white/10 rounded-full blur-2xl"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: index * 0.2
                    }}
                  />
                  <motion.div
                    className="absolute -left-4 -top-4 w-24 h-24 bg-white/10 rounded-full blur-2xl"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.3, 0.6, 0.3],
                    }}
                    transition={{
                      duration: 5,
                      repeat: Infinity,
                      ease: "easeInOut",
                      delay: index * 0.3
                    }}
                  />
                  
                  {/* Content */}
                  <div className="relative z-10">
                    <motion.div 
                      className={`${feature.iconBg} ${feature.iconColor} p-4 rounded-xl inline-block mb-4`}
                      whileHover={{ scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      {feature.icon}
                    </motion.div>
                    <h3 className="text-xl font-semibold mb-3 text-gray-900">{feature.title}</h3>
                    <p className="text-gray-700">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Floating Stats */}
            <motion.div 
              className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7, delay: 0.7 }}
            >
              {[
                { number: "24/7", label: "Support Coverage" },
                { number: "< 15min", label: "Response Time" },
                { number: "99.9%", label: "Uptime Guarantee" }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="bg-white/80 backdrop-blur-sm p-6 rounded-xl border border-gray-100 text-center"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <motion.div
                    className="text-3xl font-bold text-[#FF1919] mb-2"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity, delay: index * 0.2 }}
                  >
                    {stat.number}
                  </motion.div>
                  <div className="text-gray-600">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="relative py-24 overflow-hidden bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            {/* Animated circles */}
            <motion.div
              className="absolute top-1/4 left-1/4 w-96 h-96 bg-teclara-primary/10 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.2, 1],
                x: [0, 30, 0],
                y: [0, -30, 0],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <motion.div
              className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"
              animate={{
                scale: [1, 1.3, 1],
                x: [0, -40, 0],
                y: [0, 40, 0],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            {/* Grid Pattern */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAgMTBMMCAwTTEwIDEwTDIwIDBNMTAgMTBMMCAyME0xMCAxMEwyMCAyMCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utb3BhY2l0eT0iMC4wNSIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9zdmc+')] bg-[length:20px_20px]" />
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-5xl mx-auto text-center mb-16">
              <motion.h2 
                className="text-3xl md:text-4xl font-bold text-white mb-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7 }}
              >
                Built for SMBs. Trusted by Ontario Businesses.
              </motion.h2>
              <motion.p 
                className="text-xl text-gray-300 mb-8"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: 0.1 }}
              >
                Unlike generic IT providers, we take full responsibility for your technology. Teclara's managed IT stack is designed for Ontario's small businesses, with local support and enterprise-grade tools.
              </motion.p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
                {[
                  {
                    title: "Microsoft",
                    description: "Cloud & Productivity",
                    icon: <Cloud className="h-8 w-8 text-indigo-400" />,
                    bgColor: "from-indigo-900/50 to-indigo-800/50",
                    iconBg: "bg-indigo-500/20",
                    iconColor: "text-indigo-400",
                    features: ["Microsoft 365", "Azure Integration", "Teams & SharePoint"]
                  },
                  {
                    title: "Google",
                    description: "Workspace & Collaboration",
                    icon: <Users className="h-8 w-8 text-cyan-400" />,
                    bgColor: "from-cyan-900/50 to-cyan-800/50",
                    iconBg: "bg-cyan-500/20",
                    iconColor: "text-cyan-400",
                    features: ["Google Workspace", "Gmail Security", "Drive & Docs"]
                  },
                  {
                    title: "ConnectWise",
                    description: "IT Service Management",
                    icon: <Settings className="h-8 w-8 text-emerald-400" />,
                    bgColor: "from-emerald-900/50 to-emerald-800/50",
                    iconBg: "bg-emerald-500/20",
                    iconColor: "text-emerald-400",
                    features: ["Help Desk", "RMM Tools", "Service Automation"]
                  },
                  {
                    title: "SentinelOne",
                    description: "Endpoint Protection",
                    icon: <Shield className="h-8 w-8 text-violet-400" />,
                    bgColor: "from-violet-900/50 to-violet-800/50",
                    iconBg: "bg-violet-500/20",
                    iconColor: "text-violet-400",
                    features: ["Device Security", "Threat Detection", "Policy Management"]
                  }
                ].map((partner, index) => (
                  <motion.div 
                    key={index}
                    className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${partner.bgColor} p-8 shadow-lg border border-white/5 hover:border-white/10 transition-all duration-300 backdrop-blur-sm`}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ y: -5, borderColor: 'rgba(255,255,255,0.1)' }}
                  >
                    {/* Background Pattern */}
                    <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTAgMTBMMCAwTTEwIDEwTDIwIDBNMTAgMTBMMCAyME0xMCAxMEwyMCAyMCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utb3BhY2l0eT0iMC4wMiIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9zdmc+')] bg-[length:20px_20px]" />
                    
                    {/* Content */}
                    <div className="relative z-10">
                      <div className={`${partner.iconBg} ${partner.iconColor} p-3 rounded-xl inline-block mb-4`}>
                        {partner.icon}
                      </div>
                      <h3 className="text-xl font-semibold text-white mb-2">{partner.title}</h3>
                      <p className="text-gray-300 mb-4">{partner.description}</p>
                      
                      {/* Features List */}
                      <ul className="space-y-2">
                        {partner.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-sm text-gray-400">
                            <CheckCircle className="h-4 w-4 text-[#FF1919] mr-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    {/* Decorative Elements */}
                    <div className="absolute -right-4 -bottom-4 w-24 h-24 bg-white/5 rounded-full blur-2xl" />
                    <div className="absolute -left-4 -top-4 w-24 h-24 bg-white/5 rounded-full blur-2xl" />
                  </motion.div>
                ))}
              </div>

              {/* Trust Indicators */}
              <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
                {[
                  {
                    title: "24/7 Support",
                    description: "Round-the-clock monitoring and response",
                    icon: <Clock className="h-6 w-6 text-[#FF1919]" />
                  },
                  {
                    title: "No Hidden Fees",
                    description: "Predictable pricing with no surprise costs",
                    icon: <DollarSign className="h-6 w-6 text-[#FF1919]" />
                  },
                  {
                    title: "Local Team",
                    description: "Oakville-based support for Ontario businesses",
                    icon: <Globe className="h-6 w-6 text-[#FF1919]" />
                  }
                ].map((indicator, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-center space-x-4 bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10 hover:border-white/20 transition-colors"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="bg-[#FF1919]/20 p-2 rounded-lg">
                      {React.cloneElement(indicator.icon, { className: "h-6 w-6 text-[#FF1919]" })}
                    </div>
                    <div>
                      <h4 className="font-semibold text-white">{indicator.title}</h4>
                      <p className="text-sm text-gray-400">{indicator.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section - Ready to Secure Your Business */}
        <section className="bg-[#0A0A0A] border-t border-[#333] py-16 text-white">
          <div className="container mx-auto px-4 max-w-7xl">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
              {/* Left: Info & Contact */}
              <div>
                <h2 className="text-3xl md:text-4xl font-extrabold mb-4 text-white">Ready to Secure Your Business?</h2>
                <p className="text-lg text-gray-300 mb-8">Get started with a free security assessment. Our experts will analyze your current security posture and provide personalized recommendations.</p>
                <div className="mb-8">
                  <h3 className="text-xl font-bold mb-2 text-white">Get in Touch</h3>
                  <p className="mb-2 text-gray-300">Schedule a consultation with our cybersecurity experts. We'll discuss your specific needs and show you how Teclara Technologies can protect your organization.</p>
                  <div className="space-y-1 text-base">
                    <div>
                      <span className="font-semibold text-white">Email Us</span>
                      <br />
                      <a href="mailto:<EMAIL>" className="text-blue-400 hover:text-blue-300 transition-colors"><EMAIL></a>
                    </div>
                    <div className="mt-2">
                      <span className="font-semibold text-white">Call Us</span>
                      <br />
                      <a href="tel:+13659965856" className="text-blue-400 hover:text-blue-300 transition-colors">(*************</a>
                    </div>
                  </div>
                </div>
                <div className="mb-8">
                  <h3 className="text-lg font-bold mb-2 text-white">Why Start With Us?</h3>
                  <ul className="list-disc list-inside text-gray-300 space-y-1">
                    <li>Free security assessment (no obligation)</li>
                    <li>30-minute expert consultation</li>
                    <li>Custom security roadmap</li>
                    <li>Implementation timeline & pricing</li>
                  </ul>
                </div>
              </div>
              {/* Right: Contact Form */}
              <div>
                <div className="bg-[#181C23] rounded-2xl p-8 shadow-lg border border-gray-700">
                  <h3 className="text-2xl font-bold mb-6 text-white">Request Your Free Assessment</h3>
                  <div className="w-full" style={{ height: '600px' }}>
                    <FilloutStandardEmbed
                      filloutId="38CVFXhpBfus"
                      inheritParameters
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      {/* Minimal Footer */}
      <footer className="bg-[#0A0A0A] border-t border-[#333] py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center space-y-4">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <img 
                src="/uploads/teclara_logo_white_text.png" 
                alt="Teclara Logo" 
                className="h-8 w-auto"
              />
            </div>

            {/* Copyright */}
            <p className="text-white/70 text-sm">
              © 2025 Teclara Technologies Inc. | <a href="/privacy-policy" className="hover:text-white transition-colors">Privacy Policy</a> | <a href="/terms-of-service" className="hover:text-white transition-colors">Terms</a> | <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">Contact</a>
            </p>
            <p className="text-white/70 text-sm">
              Build: <span className="italic">{buildNumber}</span>
            </p>
          </div>
        </div>
      </footer>
    </>
  );
};

export default ManagedITLanding; 