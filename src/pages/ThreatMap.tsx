import React from 'react';
import { motion } from 'framer-motion';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Button } from "@/components/ui/button";
import { ButtonLink } from "@/components/ui/button-link";
import { Helmet } from 'react-helmet-async';
import { Star } from 'lucide-react';
import LeadGenerationButton from '@/components/LeadGenerationButton';

const ThreatMap = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
      <Helmet>
        <title>Live Global Threat Map for Oakville, Hamilton, Milton, St. Catharines & Halton Region | Teclara Technologies</title>
        <meta name="description" content="Live global cyber threat map showing real-time attacks targeting businesses. See current threat activity, attack patterns, and security insights from Teclara's 24/7 SOC monitoring." />
      </Helmet>
      <Navbar />

      {/* Hero Section */}
      <section className="pt-44 pb-16 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] relative overflow-hidden">
        {/* Brand Red Glow Background Effect - Subtle */}
        <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-[#FF1717]/15 rounded-full blur-3xl z-0" />

        <div className="max-w-7xl mx-auto text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
              Live Global <span className="text-[#6B8EF5]">Threat</span> Map
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-medium">
              Real-time visualization of cyber threats and attacks happening around the world
            </p>
          </motion.div>
        </div>
      </section>

      {/* Bitdefender Threat Map Embed */}
      <div className="w-full flex justify-center mt-8">
        <iframe
          src="https://threatmap.bitdefender.com/"
          frameBorder="0"
          className="w-full max-w-[1400px] border-none rounded-2xl bg-[#14213D] block
                     h-[120vw] md:h-[70vw]
                     min-h-[500px] md:min-h-[400px]
                     max-h-[800px] md:max-h-[900px]"
          title="Bitdefender Threat Map Live Threats"
          allowFullScreen
          sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
          loading="lazy"
        />
      </div>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `
              linear-gradient(rgba(52, 197, 182, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(52, 197, 182, 0.1) 1px, transparent 1px)
            `,
              backgroundSize: "50px 50px",
            }}
          />
        </div>

        <div className="relative container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-2xl mx-auto"
          >
            <div className="inline-flex items-center bg-[#6B8EF5]/10 backdrop-blur-sm text-[#6B8EF5] text-sm font-medium px-4 py-2 rounded-full mb-6">
              <Star className="h-4 w-4 mr-2" />
              Get Started Today
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white tracking-wide">
              Ready To <span className="text-[#6B8EF5]">Protect</span> Your Business?
            </h2>
            <p className="text-lg text-gray-300 mb-8 leading-relaxed font-medium">
              Don't wait until you become a statistic. Schedule a security assessment with our experts today.
            </p>
            <LeadGenerationButton
              formType="SECURITY_REVIEW"
              location="Threat Map Bottom CTA"
              buttonText="Schedule Your Free Security Review"
              className="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
            />
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default ThreatMap; 