import { motion } from "framer-motion";
import {
  Shield,
  CheckCircle,
  TrendingUp,
  Scale,
  FileText,
  Landmark,
  Rocket,
  BookOpen,
} from "lucide-react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Card, CardContent } from "@/components/ui/card";
import CanonicalUrl from "@/components/CanonicalUrl";
import { Helmet } from "react-helmet-async";
import { createLocalBusinessSchema, createFAQSchema } from "@/utils/schemaData";
import LeadGenerationButton from "@/components/LeadGenerationButton";

const About = () => {
  // Create FAQ data
  const aboutFAQs = [
    {
      question: "What makes Teclara different from other IT providers?",
      answer:
        "We combine enterprise-grade technology with local expertise, offering comprehensive solutions specifically designed for SMBs. Our security-first approach and flat-rate pricing model set us apart.",
    },
    {
      question: "Where is Teclara located?",
      answer:
        "We're based in Oakville, Ontario, and serve businesses throughout the Halton Region, including Hamilton, Milton, and St. Catharines.",
    },
    {
      question: "What industries do you serve?",
      answer:
        "We serve a wide range of industries including legal firms, financial services, consulting firms, architecture firms, and startups, with specialized solutions for each sector.",
    },
    {
      question: "What is your approach to customer service?",
      answer:
        "We provide 24/7 support, proactive monitoring, and regular check-ins to ensure your IT infrastructure runs smoothly. Our local team is always available to address your needs promptly.",
    },
  ];

  // Create schema data
  const localBusinessSchema = createLocalBusinessSchema({
    name: "Teclara Technologies",
    description:
      "Security-first MSP delivering enterprise-grade IT solutions to SMBs across Ontario",
    address: {
      street: "123 Business Street",
      city: "Oakville",
      region: "ON",
      postalCode: "L6H 0C3",
      country: "CA",
    },
    geo: {
      latitude: 43.4675,
      longitude: -79.6877,
    },
    phone: "******-123-4567",
    email: "<EMAIL>",
    priceRange: "$$",
    openingHours: [
      "Monday 09:00-17:00",
      "Tuesday 09:00-17:00",
      "Wednesday 09:00-17:00",
      "Thursday 09:00-17:00",
      "Friday 09:00-17:00",
    ],
  });

  const aboutSchema = {
    "@context": "https://schema.org",
    "@type": "AboutPage",
    name: "About Teclara Technologies",
    description:
      "Learn about Teclara Technologies, a security-first MSP delivering enterprise-grade IT solutions to SMBs across Ontario. Led by experts with Fortune 500 experience.",
    mainEntity: {
      "@type": "Organization",
      name: "Teclara Technologies",
      description:
        "Security-first MSP delivering enterprise-grade IT solutions to SMBs across Ontario",
      foundingDate: "2023",
      founders: [
        {
          "@type": "Person",
          name: "Teclara Leadership Team",
          jobTitle: "Executive Team",
        },
      ],
      awards: ["Top MSP in Halton Region", "Excellence in Cybersecurity"],
      knowsAbout: [
        "Cybersecurity",
        "Managed IT Services",
        "Business Continuity",
        "Cloud Solutions",
        "Compliance",
      ],
      areaServed: [
        {
          "@type": "City",
          name: "Oakville",
        },
        {
          "@type": "City",
          name: "Hamilton",
        },
        {
          "@type": "City",
          name: "Milton",
        },
        {
          "@type": "City",
          name: "St. Catharines",
        },
      ],
    },
  };

  const faqSchema = createFAQSchema(aboutFAQs);

  const industries = [
    { name: "Legal Firms", icon: <Scale className="h-10 w-10" /> },
    { name: "Accounting Firms", icon: <FileText className="h-10 w-10" /> },
    { name: "Financial Services", icon: <Landmark className="h-10 w-10" /> },
    { name: "Startups", icon: <Rocket className="h-10 w-10" /> },
    { name: "Architecture Firms", icon: <BookOpen className="h-10 w-10" /> },
  ];

  return (
    <>
      <Helmet>
        <title>
          About Teclara Technologies | Cybersecurity Experts in Oakville,
          Hamilton, Milton, St. Catharines & Halton Region
        </title>
        <meta
          name="description"
          content="Meet Teclara Technologies: Fortune 500 cybersecurity expertise for Canadian SMBs. Founded by enterprise architects, delivering security-first IT solutions with local support."
        />

        {/* Page specific Open Graph */}
        <meta
          property="og:title"
          content="About Teclara Technologies | Local Cybersecurity Experts"
        />
        <meta
          property="og:description"
          content="Meet Teclara Technologies: A security-first MSP delivering enterprise-grade IT solutions with Fortune 500 expertise to Ontario businesses."
        />
        <meta property="og:url" content="https://teclara.tech/about" />
        <meta property="og:type" content="website" />
        <meta
          property="og:image"
          content="https://teclara.tech/uploads/teclara_logo_black_text.png"
        />
        <meta property="og:image:alt" content="Teclara Technologies About Us" />

        {/* Page specific Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content="About Teclara Technologies | Local Cybersecurity Experts"
        />
        <meta
          name="twitter:description"
          content="Meet Teclara Technologies: A security-first MSP delivering enterprise-grade IT solutions with Fortune 500 expertise to Ontario businesses."
        />
        <meta
          name="twitter:image"
          content="https://teclara.tech/uploads/teclara_logo_black_text.png"
        />

        {/* Additional meta tags */}
        <meta
          name="keywords"
          content="about Teclara, cybersecurity experts, managed IT services, local MSP, Oakville IT company, Hamilton IT services, Milton IT support, St. Catharines IT solutions, Halton Region IT provider"
        />
        <meta name="author" content="Teclara Technologies" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="en-CA" />
        <meta name="geo.region" content="CA-ON" />
        <meta name="geo.placename" content="Oakville, Ontario" />
        <meta name="geo.position" content="43.4675;-79.6877" />
        <meta name="ICBM" content="43.4675, -79.6877" />

        {/* Resource hints */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />

        {/* Schema.org data */}
        <script type="application/ld+json">
          {JSON.stringify(localBusinessSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(aboutSchema)}
        </script>
        <script type="application/ld+json">{JSON.stringify(faqSchema)}</script>
      </Helmet>
      <CanonicalUrl path="/about" />
      <div className="min-h-screen bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
        <Navbar />

        {/* What Makes Teclara Different section */}
        <section className="pt-44 pb-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] relative overflow-hidden">
          {/* Brand Red Glow Background Effect - Subtle */}
          <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-[#FF1717]/15 rounded-full blur-3xl z-0" />
          <div className="max-w-7xl mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
                Cybersecurity That's Built For{' '}
                <span className="text-[#6B8EF5]">Business</span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-medium mb-8">
                Protecting your reputation, securing your data, and ensuring your business never stops.
              </p>
              <div className="flex justify-center">
                <LeadGenerationButton
                  formType="SECURITY_REVIEW"
                  location="About Page - First Section"
                  buttonText="Get Your Free Security Review"
                  className="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
                />
              </div>
            </motion.div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                <div className="h-16 w-16 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center mb-6 mx-auto">
                  <Shield className="h-8 w-8 text-[#6B8EF5]" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4 text-center tracking-wide">
                  Cybersecurity-First
                </h3>
                <p className="text-gray-300 leading-relaxed text-center font-medium">
                  We lead with protection. Every service we deliver—whether it's
                  endpoint support or cloud management—is secured by default and
                  monitored by our 24/7 SOC.
                </p>
              </div>
              <div className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                <div className="h-16 w-16 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center mb-6 mx-auto">
                  <TrendingUp className="h-8 w-8 text-[#6B8EF5]" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4 text-center tracking-wide">
                  Aligned To Growth
                </h3>
                <p className="text-gray-300 leading-relaxed text-center font-medium">
                  Our services scale with you. From proactive support to vCIO
                  guidance, we help you avoid tech debt and invest in solutions
                  that future-proof your business.
                </p>
              </div>
              <div className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                <div className="h-16 w-16 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center mb-6 mx-auto">
                  <CheckCircle className="h-8 w-8 text-[#6B8EF5]" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4 text-center tracking-wide">
                  No Surprise Pricing
                </h3>
                <p className="text-gray-300 leading-relaxed text-center font-medium">
                  Forget hidden fees or change orders. We operate on fixed
                  monthly pricing, so you always know what you're paying—and
                  what you're getting.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Founder Story */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
          <div className="container px-4 mx-auto max-w-7xl">
            {/* Header */}
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-bold mb-3 tracking-wide">
                From Big Consulting To{" "}
                <span className="text-[#6B8EF5]">SMB Defense</span>
              </h2>
              <p className="text-lg text-[#6B8EF5] font-semibold tracking-wide">
                Founder Story
              </p>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">

              {/* Founder Image - Now Larger and More Prominent */}
              <div className="lg:col-span-1 flex justify-center lg:justify-start">
                <div className="relative">
                  <div className="transition-transform duration-500 ease-in-out transform hover:scale-105 hover:-translate-y-2 shadow-2xl hover:shadow-[0_8px_32px_0_rgba(107,142,245,0.25),0_0_0_8px_rgba(107,142,245,0.08)] rounded-xl group relative">
                    <img
                      src="/uploads/wadhah_profile.jpeg"
                      alt="Wadhah Hussain, Founder of Teclara Technologies"
                      className="rounded-xl shadow-lg w-full max-w-sm h-auto group-hover:shadow-[0_8px_32px_0_rgba(107,142,245,0.35),0_0_0_12px_rgba(107,142,245,0.12)] group-hover:ring-4 group-hover:ring-[#6B8EF5]/30 transition-all duration-500"
                      style={{ boxShadow: "0 4px 24px 0 rgba(107,142,245,0.15)" }}
                    />
                    {/* Decorative glow */}
                    <div
                      className="absolute inset-0 rounded-xl pointer-events-none group-hover:opacity-100 opacity-0 transition-opacity duration-500"
                      style={{
                        boxShadow: `0 0 40px 10px rgba(107, 142, 245, 0.1)`,
                      }}
                    ></div>
                  </div>

                  {/* Founder Info Card */}
                  <div className="mt-4 bg-[#060d25]/80 backdrop-blur-sm rounded-xl p-5 border border-[#6B8EF5]/20">
                    <h3 className="text-xl font-bold text-white mb-1">Wadhah Hussain</h3>
                    <p className="text-[#6B8EF5] font-semibold mb-2">Founder & CEO</p>
                    <div className="space-y-2 text-sm text-gray-300">
                      <p className="font-medium">• Former Principal Enterprise Architect at OMERS</p>
                      <p className="font-medium">• Ex-Deloitte, Accenture, Slalom</p>
                      <p className="font-medium">• Enterprise Security Specialist</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Story Content - Now Better Organized */}
              <div className="lg:col-span-2 space-y-4">

                {/* Enterprise Experience */}
                <div className="bg-[#060d25]/40 backdrop-blur-sm rounded-xl p-6 border border-[#6B8EF5]/10">
                  <h3 className="text-2xl font-bold text-white mb-3 flex items-center">
                    <div className="w-2 h-8 bg-[#6B8EF5] rounded-full mr-4"></div>
                    Enterprise Experience
                  </h3>
                  <p className="text-gray-300 font-medium leading-relaxed">
                    Throughout my career, I've delivered enterprise-grade cloud and infrastructure solutions,
                    cybersecurity, and governance initiatives for Canada's largest organizations, including
                    Enbridge Gas, lululemon, GreenShield Canada, Teck Resources, TD Bank, EQ Bank, FSRA,
                    Four Seasons Hotels, and Telus through my work at Deloitte, Accenture, and Slalom,
                    with my most recent role as Principal Enterprise Architect at OMERS.
                  </p>
                </div>

                {/* The Gap */}
                <div className="bg-[#060d25]/40 backdrop-blur-sm rounded-xl p-6 border border-[#FF1717]/10">
                  <h3 className="text-2xl font-bold text-white mb-3 flex items-center">
                    <div className="w-2 h-8 bg-[#FF1717] rounded-full mr-4"></div>
                    The Gap I Discovered
                  </h3>
                  <p className="text-gray-300 font-medium leading-relaxed">
                    Working with these industry leaders gave me invaluable insight into how top-tier companies
                    leverage technology. But I saw a critical gap: Small and mid-sized Canadian businesses
                    weren't getting the same level of IT strategy, cybersecurity, and proactive support
                    that big enterprises can afford. Too often, IT was treated as a reactive service
                    rather than a strategic enabler.
                  </p>
                </div>

                {/* The Solution */}
                <div className="bg-[#060d25]/40 backdrop-blur-sm rounded-xl p-6 border border-[#6B8EF5]/10">
                  <h3 className="text-2xl font-bold text-white mb-3 flex items-center">
                    <div className="w-2 h-8 bg-[#6B8EF5] rounded-full mr-4"></div>
                    The Teclara Solution
                  </h3>
                  <p className="text-gray-300 font-medium leading-relaxed mb-3">
                    That's why I founded Teclara Technologies—to bring enterprise-grade IT strategy and
                    security to SMBs across Canada. Canadian businesses deserve more than basic IT support.
                    They need a trusted partner who understands their industry, anticipates risks, and helps
                    them use technology to operate efficiently and securely.
                  </p>
                  <p className="text-gray-300 font-medium leading-relaxed">
                    At Teclara, we don't just manage IT—we secure reputations, protect businesses from
                    lawsuits and data breaches, and empower firms to scale with confidence. Because in
                    today's world, IT isn't just about keeping the lights on—it's about keeping your
                    business safe, competitive, and thriving.
                  </p>
                </div>

                {/* Call to Action */}
                <div className="text-center pt-4">
                  <p className="text-xl text-gray-300 font-medium mb-4 italic">
                    "Let's build a smarter, more secure future together."
                  </p>
                  <div className="inline-flex items-center gap-4 bg-[#6B8EF5]/10 backdrop-blur-sm rounded-full px-8 py-4 border border-[#6B8EF5]/20">
                    <div className="w-12 h-12 rounded-full bg-[#6B8EF5]/20 flex items-center justify-center">
                      <span className="text-[#6B8EF5] font-bold text-lg">W</span>
                    </div>
                    <div className="text-left">
                      <p className="text-white font-bold">Wadhah Hussain</p>
                      <p className="text-[#6B8EF5] text-sm font-semibold">Founder & CEO, Teclara Technologies</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Who We Serve */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white relative overflow-hidden">
          <div className="container px-4 mx-auto relative">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white tracking-wide">
                Who We <span className="text-[#6B8EF5]">Serve</span>
              </h2>
              <p className="text-gray-300 font-medium">
                We specialize in serving Canadian organizations with high
                security needs and compliance requirements.
              </p>
            </div>
            <div className="flex flex-wrap md:flex-nowrap gap-6 justify-center">
              {industries.map((industry, index) => (
                <Card
                  key={index}
                  className="border border-[#6B8EF5]/20 bg-[#060d25]/80 hover:bg-[#060d25]/90 shadow-md hover:shadow-xl hover:scale-105 transition-all duration-300 flex-1 min-w-[180px] max-w-xs group"
                >
                  <CardContent className="p-6 flex flex-col items-center text-center">
                    <div className="h-20 w-20 rounded-full bg-gradient-to-br from-[#6B8EF5]/10 to-[#6B8EF5]/5 flex items-center justify-center mb-4 group-hover:from-[#6B8EF5]/20 group-hover:to-[#6B8EF5]/10 transition-all duration-300">
                      <div className="text-[#6B8EF5] group-hover:scale-110 transition-transform duration-300">
                        {industry.icon}
                      </div>
                    </div>
                    <h3 className="font-semibold text-lg text-white">
                      {industry.name}
                    </h3>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-16 md:py-24 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
          <div className="container px-4 mx-auto text-center max-w-3xl space-y-6">
            <h2 className="text-3xl md:text-4xl font-bold tracking-wide">
              Let's Build A{" "}
              <span className="text-[#6B8EF5]">Safer Future</span> For Your
              Business
            </h2>
            <p className="text-lg text-gray-300 font-medium">
              We don't just manage your IT. We secure it, scale it, and align it
              to your business outcomes.
            </p>
            <div className="flex flex-wrap gap-4 justify-center pt-4">
              <LeadGenerationButton
                formType="SECURITY_REVIEW"
                location="About Page - Final CTA Section"
                buttonText="Get Your Free Security Review"
                className="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
              />
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default About;
