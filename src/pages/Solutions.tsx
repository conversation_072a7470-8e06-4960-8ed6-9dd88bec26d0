import { CheckCircle, Shield, HardDrive, Cloud, AlertTriangle, Activity, FileText, User, RefreshCw, Headphones, TrendingUp, DollarSign } from 'lucide-react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import CanonicalUrl from '@/components/CanonicalUrl';
import { Helmet } from 'react-helmet-async';
import { 
  createServiceSchema, 
  createLocalBusinessSchema,
  createFAQSchema 
} from '@/utils/schemaData';
import LeadGenerationButton from '../components/LeadGenerationButton';

export default function Solutions() {
  // Create FAQ data
  const faqs = [
    {
      question: "What makes Teclara's cybersecurity solutions different?",
      answer: "Our solutions combine enterprise-grade technology with local expertise, offering 24/7 monitoring, compliance support, and proactive threat detection specifically designed for SMBs."
    },
    {
      question: "How does your managed IT service work?",
      answer: "We provide comprehensive IT management including 24/7 monitoring, help desk support, proactive maintenance, and strategic planning, all delivered through a flat-rate pricing model."
    },
    {
      question: "What areas do you serve?",
      answer: "We serve businesses in Oakville, Hamilton, Milton, St. Catharines, and the Halton Region, providing local expertise with enterprise-grade solutions."
    },
    {
      question: "Do you offer compliance support?",
      answer: "Yes, we provide comprehensive compliance support including gap analysis, implementation, and ongoing maintenance for various industry standards and regulations."
    }
  ];

  // Create schema data
  const localBusinessSchema = createLocalBusinessSchema({
    name: "Teclara Technologies",
    description: "Enterprise-grade cybersecurity and managed IT services for Canadian SMBs",
    address: {
      street: "123 Business Street",
      city: "Oakville",
      region: "ON",
      postalCode: "L6H 0C3",
      country: "CA"
    },
    geo: {
      latitude: 43.4675,
      longitude: -79.6877
    },
    phone: "******-123-4567",
    email: "<EMAIL>",
    priceRange: "$$",
    openingHours: [
      "Monday 09:00-17:00",
      "Tuesday 09:00-17:00",
      "Wednesday 09:00-17:00",
      "Thursday 09:00-17:00",
      "Friday 09:00-17:00"
    ]
  });

  const serviceSchema = createServiceSchema(
    "Enterprise IT & Security Solutions",
    "Comprehensive IT and security solutions for SMBs in Oakville, Hamilton, Milton, St. Catharines, and Halton Region. 24/7 monitoring, managed IT, cybersecurity, and expert support.",
    "IT Services & Security",
    {
      areaServed: ["Oakville", "Hamilton", "Milton", "St. Catharines", "Halton Region"],
      serviceOutput: [
        "24/7 Security Monitoring",
        "Compliance Support",
        "Expert IT Support",
        "Proactive Maintenance",
        "Business Continuity",
        "Cloud Solutions",
        "Network Security",
        "Endpoint Protection",
        "Data Backup & Recovery",
        "Staff Training"
      ]
    }
  );

  const faqSchema = createFAQSchema(faqs);

  const coreServices = [
    {
      icon: Shield,
      title: "24/7 Security Operations",
      subtitle: "Managed SOC",
      description: "Round-the-clock monitoring and threat response to minimize breach impact and downtime",
      details: [
        "24/7 threat detection and response",
        "Advanced endpoint protection", 
        "Network traffic analysis",
        "Real-time alerting system"
      ]
    },
    {
      icon: Activity,
      title: "Threat Detection",
      subtitle: "AI-Powered Defense",
      description: "AI-powered threat intelligence and scanning to catch attacks before they spread",
      details: [
        "Advanced threat intelligence",
        "Behavioral analysis",
        "Real-time threat scanning",
        "Automated response actions"
      ]
    },
    {
      icon: FileText,
      title: "Compliance & Security",
      subtitle: "Regulatory Readiness",
      description: "Audit-ready security controls to meet industry and client compliance requirements",
      details: [
        "Compliance gap analysis",
        "Policy development",
        "Audit preparation",
        "Regulatory reporting"
      ]
    },
    {
      icon: User,
      title: "Security Training",
      subtitle: "Human Risk Reduction",
      description: "Awareness training and simulations to reduce human error and phishing risk",
      details: [
        "Phishing simulations",
        "Security awareness training",
        "Compliance training",
        "Custom training programs"
      ]
    },
    {
      icon: Cloud,
      title: "Cloud Security",
      subtitle: "SaaS & Email Protection",
      description: "Microsoft 365 and Google Workspace security with automated threat detection and controls",
      details: [
        "Cloud access security",
        "Email security",
        "Data loss prevention",
        "Cloud backup solutions"
      ]
    },
    {
      icon: AlertTriangle,
      title: "Incident Response",
      subtitle: "Breach Containment",
      description: "Expert-led investigation and rapid containment of active security incidents",
      details: [
        "Rapid incident response",
        "Forensic investigation",
        "Breach containment",
        "Recovery planning"
      ]
    }
  ];

  const additionalServices = [
    {
      icon: RefreshCw,
      title: 'Proactive Maintenance',
      subtitle: 'Downtime Prevention',
      description: 'Automated patching and updates to reduce downtime and security risk',
      details: [
        'Automated system updates',
        'Regular maintenance schedules',
        'Performance optimization',
        'System health monitoring',
      ],
    },
    {
      icon: Headphones,
      title: 'Help Desk Support',
      subtitle: 'User Productivity',
      description: 'Responsive expert support to resolve user issues quickly and efficiently',
      details: [
        '24/7 support availability',
        'Remote assistance',
        'On-site support',
        'Ticket tracking system',
      ],
    },
    {
      icon: TrendingUp,
      title: 'Strategic IT Planning',
      subtitle: 'vCIO Guidance',
      description: 'vCIO services to align IT investments with business priorities',
      details: [
        'Technology roadmapping',
        'Budget planning',
        'Vendor management',
        'Strategic alignment',
      ],
    },
    {
      icon: DollarSign,
      title: 'Predictable Pricing',
      subtitle: 'Flat-Rate Simplicity',
      description: 'Flat monthly rates for full-service IT—no surprise costs',
      details: [
        'Transparent pricing',
        'No hidden fees',
        'Predictable budgeting',
        'Service level agreements',
      ],
    },
    {
      icon: Cloud,
      title: 'Cloud Solutions',
      subtitle: 'Cloud Optimization',
      description: 'Seamless management and optimization of your cloud environments',
      details: [
        'Cloud migration',
        'Cloud optimization',
        'Cost management',
        'Security controls',
      ],
    },
    {
      icon: HardDrive,
      title: 'Backup & Recovery',
      subtitle: 'Business Continuity',
      description: 'Automated data backups with fast recovery to maintain business continuity',
      details: [
        'Automated backups',
        'Disaster recovery',
        'Data protection',
        'Recovery testing',
      ],
    },
  ];

  return (
    <>
      <Helmet>
        <title>Cybersecurity & IT Solutions for Oakville, Hamilton, Milton, St. Catharines & Halton Region | Teclara Technologies</title>
        <meta name="description" content="Comprehensive cybersecurity and IT solutions: 24/7 SOC monitoring, threat detection, compliance support, managed IT services, and incident response for business protection." />
        
        {/* Page specific Open Graph */}
        <meta property="og:title" content="Enterprise IT & Security Solutions | Teclara Technologies" />
        <meta property="og:description" content="Comprehensive IT and security solutions with 24/7 monitoring, managed services, and expert local support for SMBs." />
        <meta property="og:url" content="https://teclara.tech/solutions" />
        <meta property="og:type" content="website" />
        <meta property="og:image" content="https://teclara.tech/uploads/teclara_logo_black_text.png" />
        <meta property="og:image:alt" content="Teclara Technologies Solutions" />
        
        {/* Page specific Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Enterprise IT & Security Solutions | Teclara Technologies" />
        <meta name="twitter:description" content="Comprehensive IT and security solutions for SMBs. 24/7 monitoring, managed IT, cybersecurity, and expert support from local specialists." />
        <meta name="twitter:image" content="https://teclara.tech/uploads/teclara_logo_black_text.png" />
        
        {/* Additional meta tags */}
        <meta name="keywords" content="cybersecurity solutions, managed IT services, business continuity, data protection, compliance, network security, cloud security, endpoint protection, Oakville, Hamilton, Milton, St. Catharines, Halton Region" />
        <meta name="author" content="Teclara Technologies" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="en-CA" />
        <meta name="geo.region" content="CA-ON" />
        <meta name="geo.placename" content="Oakville, Ontario" />
        <meta name="geo.position" content="43.4675;-79.6877" />
        <meta name="ICBM" content="43.4675, -79.6877" />
        
        {/* Resource hints */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* Schema.org data */}
        <script type="application/ld+json">
          {JSON.stringify(localBusinessSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(serviceSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(faqSchema)}
        </script>
      </Helmet>
      <CanonicalUrl path="/solutions" />
      
      <div className="min-h-screen bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
        <Navbar />


        {/* Core Security Services */}
        <section className="pt-44 pb-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] relative overflow-hidden">
          {/* Brand Red Glow Background Effect - Subtle */}
          <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-[#FF1717]/15 rounded-full blur-3xl z-0" />

          <div className="max-w-7xl mx-auto relative z-10">
            <div className="text-center mb-20 animate-in fade-in duration-700">
              <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
                Comprehensive Security &{' '}
                <span className="text-[#6B8EF5]">IT Solutions</span>
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-medium">
                End-to-end IT solutions designed to keep your business running smoothly and securely.
              </p>
              <div className="mt-8">
                <LeadGenerationButton
                  formType="IT_CONSULT"
                  location="Solutions Page Header CTA"
                  buttonText="Get Your Free Security Assessment"
                  className="bg-black text-white border-2 border-[#FF1717] px-4 sm:px-8 py-3 text-sm sm:text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)] w-auto max-w-xs sm:max-w-md mx-auto"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {coreServices.map((service, index) => {
                const Icon = service.icon;
                return (
                  <div
                    key={index}
                    className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/50 transition-all duration-300 hover:bg-[#060d25]/90 animate-in fade-in slide-in-from-bottom-4 duration-500"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="bg-[#6B8EF5]/10 p-4 rounded-xl inline-block mb-6">
                      <Icon className="h-8 w-8 text-[#6B8EF5]" />
                    </div>
                    <h3 className="text-2xl font-bold mb-2 tracking-wide">{service.title}</h3>
                    <p className="text-sm font-semibold text-[#FEC400] mb-4">{service.subtitle}</p>
                    <p className="text-gray-300 mb-6 leading-relaxed font-medium">{service.description}</p>

                    <ul className="space-y-3">
                      {service.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start text-sm text-gray-300">
                          <CheckCircle className="h-4 w-4 text-[#6B8EF5] mr-3 flex-shrink-0 mt-0.5" />
                          <span>{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Additional Services */}
        <section className="py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-20 animate-in fade-in duration-700 delay-300">
              <div className="inline-flex items-center gap-2 bg-[#6B8EF5]/10 text-[#6B8EF5] px-6 py-3 rounded-full text-sm font-medium mb-8 border border-[#6B8EF5]/20">
                Additional Services
              </div>
              <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
                Complete IT Management{' '}
                <span className="text-[#6B8EF5]">Solutions</span>
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-medium">
                Beyond our core offerings, we provide a range of services to cover all your technology needs.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {additionalServices.map((service, index) => {
                const Icon = service.icon;
                return (
                  <div
                    key={index}
                    className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/50 transition-all duration-300 hover:bg-[#060d25]/90 animate-in fade-in slide-in-from-bottom-4 duration-500"
                    style={{ animationDelay: `${600 + index * 100}ms` }}
                  >
                    <div className="bg-[#6B8EF5]/10 p-4 rounded-xl inline-block mb-6">
                      <Icon className="h-8 w-8 text-[#6B8EF5]" />
                    </div>
                    <h3 className="text-2xl font-bold mb-2 tracking-wide">{service.title}</h3>
                    <p className="text-sm font-semibold text-[#FEC400] mb-4">{service.subtitle}</p>
                    <p className="text-gray-300 mb-6 leading-relaxed font-medium">{service.description}</p>

                    <ul className="space-y-3">
                      {service.details.map((detail, idx) => (
                        <li key={idx} className="flex items-start text-sm text-gray-300">
                          <CheckCircle className="h-4 w-4 text-[#6B8EF5] mr-3 flex-shrink-0 mt-0.5" />
                          <span>{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Case Study Section */}
        <section className="py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16 animate-in fade-in duration-700 delay-1000">
              <div className="inline-flex items-center gap-2 bg-[#6B8EF5]/10 text-[#6B8EF5] px-6 py-3 rounded-full text-sm font-medium mb-8 border border-[#6B8EF5]/20">
                Success Story
              </div>
              <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
                How We Helped A Client Avoid A{' '}
                <span className="text-[#6B8EF5]">Ransomware Attack</span>
              </h2>
            </div>

            <div className="max-w-4xl mx-auto animate-in fade-in slide-in-from-bottom-4 duration-700 delay-1200">
              <div className="bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20">
                <div className="space-y-8">
                  <div className="flex flex-col sm:flex-row items-start gap-6">
                    <div className="w-16 h-16 rounded-xl bg-[#FEC400]/10 flex items-center justify-center flex-shrink-0 mx-auto sm:mx-0">
                      <AlertTriangle className="h-8 w-8 text-[#FEC400]" />
                    </div>
                    <div className="text-center sm:text-left">
                      <h3 className="text-2xl font-bold mb-4 tracking-wide">The Nightmare Scenario</h3>
                      <p className="text-lg text-gray-300 leading-relaxed font-medium">
                        A manufacturing company's entire production line could be shut down by ransomware. Hackers could encrypt all their CAD files, customer orders, and financial records, demanding $2 million in ransom. Without backups, they'd face bankruptcy within weeks as customers flee to competitors.
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start gap-6">
                    <div className="w-16 h-16 rounded-xl bg-[#6B8EF5]/10 flex items-center justify-center flex-shrink-0 mx-auto sm:mx-0">
                      <Shield className="h-8 w-8 text-[#6B8EF5]" />
                    </div>
                    <div className="text-center sm:text-left">
                      <h3 className="text-2xl font-bold mb-4 tracking-wide">How Teclara Would Prevent This Disaster</h3>
                      <p className="text-lg text-gray-300 leading-relaxed font-medium">
                        Our AI-powered threat detection would stop the ransomware before it encrypts a single file. 24/7 monitoring catches attacks in seconds, automated backups ensure instant recovery, and our security training prevents staff from clicking malicious links. The attack fails completely.
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row items-start gap-6">
                    <div className="w-16 h-16 rounded-xl bg-[#6B8EF5]/10 flex items-center justify-center flex-shrink-0 mx-auto sm:mx-0">
                      <CheckCircle className="h-8 w-8 text-[#6B8EF5]" />
                    </div>
                    <div className="text-center sm:text-left">
                      <h3 className="text-2xl font-bold mb-4 tracking-wide">The Results: Business Saved</h3>
                      <p className="text-lg text-gray-300 leading-relaxed font-medium">
                        The company never loses a single file or minute of production time. While competitors suffer ransomware attacks and go out of business, this company thrives with zero security incidents. Customers trust them more, insurance rates stay low, and profits soar.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-12 text-center">
                  <LeadGenerationButton
                    formType="IT_CONSULT"
                    location="Solutions Page Bottom CTA"
                    buttonText="Schedule Your Free Consultation"
                    className="bg-black text-white border-2 border-[#FF1717] px-4 sm:px-8 py-3 text-sm sm:text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)] w-auto max-w-xs sm:max-w-md mx-auto"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <Footer />
      </div>
    </>
  );
}
