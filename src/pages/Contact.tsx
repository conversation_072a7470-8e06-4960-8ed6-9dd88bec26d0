import React from "react";
import { motion } from "framer-motion";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Mail, Phone, MapPin, Clock, Linkedin } from "lucide-react";
import CanonicalUrl from "@/components/CanonicalUrl";
import { Helmet } from "react-helmet-async";
import { FilloutStandardEmbed } from "@fillout/react";

const Contact = () => {
  return (
    <>
      <Helmet>
        <title>
          Contact Teclara Technologies | Cybersecurity & IT in Oakville,
          Hamilton, Milton, St. Catharines & Halton Region
        </title>
        <meta
          name="description"
          content="Contact Teclara Technologies for expert cybersecurity and IT support. Get a free security assessment, discuss your IT needs, or schedule a consultation with our specialists."
        />
      </Helmet>
      <CanonicalUrl path="/contact" />
      <div className="min-h-screen bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
        <Navbar />

        {/* Contact Grid Section */}
        <section className="pt-44 pb-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] relative overflow-hidden">
          {/* Red Glow Background Effect */}
          <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-red-500/30 rounded-full blur-3xl z-0" />

          {/* Background Grid Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `
                linear-gradient(rgba(52, 197, 182, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(52, 197, 182, 0.1) 1px, transparent 1px)
              `,
                backgroundSize: "50px 50px",
              }}
            />
          </div>

          {/* Gradient Orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#6B8EF5]/10 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-[#FEC400]/15 rounded-full blur-3xl" />

          <div className="relative z-10 max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
                Ready To <span className="text-[#6B8EF5]">Get Started?</span>
              </h2>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Info */}
              <div className="space-y-8">
                <div>
                  <h3 className="text-2xl font-bold mb-6 text-white tracking-wide">
                    Contact Information
                  </h3>
                  <p className="text-gray-300 mb-8 font-medium">
                    Our security experts are ready to help you protect your
                    business.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center">
                        <Phone className="h-4 w-4 text-[#6B8EF5]" />
                      </div>
                      <h4 className="font-semibold text-white tracking-wide">Phone</h4>
                    </div>
                    <p className="text-gray-300 text-sm pl-10 font-medium">
                      +****************
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center">
                        <Mail className="h-4 w-4 text-[#6B8EF5]" />
                      </div>
                      <h3 className="font-semibold text-white tracking-wide">Email</h3>
                    </div>
                    <p className="text-gray-300 text-sm pl-10 font-medium">
                      <EMAIL>
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center">
                        <MapPin className="h-4 w-4 text-[#6B8EF5]" />
                      </div>
                      <h3 className="font-semibold text-white tracking-wide">LOCATION</h3>
                    </div>
                    <p className="text-gray-300 text-sm pl-10 font-medium">
                      Oakville, Ontario, Canada
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center">
                        <Clock className="h-4 w-4 text-[#6B8EF5]" />
                      </div>
                      <h3 className="font-semibold text-white tracking-wide">Hours</h3>
                    </div>
                    <p className="text-gray-300 text-sm pl-10 font-medium">
                      Mon-Fri: 9:00 AM - 5:00 PM ET
                    </p>
                  </div>

                  <div className="space-y-2 col-span-1 md:col-span-2">
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-full bg-[#6B8EF5]/10 flex items-center justify-center">
                        <Linkedin className="h-4 w-4 text-[#6B8EF5]" />
                      </div>
                      <h3 className="font-semibold text-white tracking-wide">LinkedIn</h3>
                    </div>
                    <a
                      href="https://linkedin.com/company/teclara"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-gray-300 pl-10 hover:text-[#6B8EF5] transition-colors font-medium"
                    >
                      linkedin.com/company/teclara
                    </a>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="bg-[#060d25]/80 backdrop-blur-sm p-8 rounded-2xl border border-[#6B8EF5]/20">
                <h3 className="text-2xl font-bold mb-6 text-white tracking-wide">
                  Get In Touch
                </h3>
                <div className="w-full" style={{ height: "480px" }}>
                  <FilloutStandardEmbed
                    filloutId="38CVFXhpBfus"
                    inheritParameters
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default Contact;
