import React, { useEffect } from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import CanonicalUrl from '@/components/CanonicalUrl';
import { Helmet } from 'react-helmet-async';

const Book = () => {
  useEffect(() => {
    window.location.href = 'https://outlook.office.com/owa/calendar/<EMAIL>/bookings/';
  }, []);

  return (
    <>
      <Helmet>
        <title>Book a Meeting | Teclara Technologies</title>
        <meta name="description" content="Schedule a free cybersecurity consultation with Teclara Technologies. Discuss your IT security needs, get expert recommendations, and protect your business today." />
      </Helmet>
      <CanonicalUrl path="/book" />
      <div className="min-h-screen flex flex-col bg-white text-black">
        <Navbar />
        <section className="flex-grow flex items-center justify-center">
          <div className="text-center py-32">
            <h1 className="text-3xl font-bold mb-4">Redirecting to <span style={{ color: '#FF1919' }}>Booking</span>...</h1>
            <p className="text-lg text-gray-700">If you are not redirected, <a href="https://outlook.office.com/owa/calendar/<EMAIL>/bookings/" className="text-[#FF1919] underline hover:text-[#D40000]" target="_blank" rel="noopener noreferrer">click here to book your meeting</a>.</p>
          </div>
        </section>
        <Footer />
      </div>
    </>
  );
};

export default Book; 