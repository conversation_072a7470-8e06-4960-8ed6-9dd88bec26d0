import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Link } from "react-router-dom";
import { Mail, Phone } from "lucide-react";
import { Helmet } from 'react-helmet-async';
import CanonicalUrl from '@/components/CanonicalUrl';
import { clarityUtils } from '@/components/Clarity';

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    // Track 404 errors for analytics
    clarityUtils.event("404_error_occurred");
    clarityUtils.setTag("error_type", "404_not_found");
    clarityUtils.setTag("attempted_path", location.pathname);
    clarityUtils.setTag("referrer", document.referrer || "direct");
  }, [location.pathname]);

  return (
    <>
      <CanonicalUrl path="/404" />
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-900 text-white">
        <Helmet>
          <title>404 - Page Not Found | Teclara Technologies</title>
          <meta name="description" content="Enterprise-grade cybersecurity and IT support for Canadian businesses. Expert solutions for data protection, compliance, and business continuity." />
          <meta name="robots" content="noindex, follow" />
          
          {/* Open Graph / Facebook */}
          <meta property="og:type" content="website" />
          <meta property="og:url" content="https://teclara.tech/404" />
          <meta property="og:title" content="404 - Page Not Found | Teclara Technologies" />
          <meta property="og:description" content="Enterprise-grade cybersecurity and IT support for Canadian businesses. Expert solutions for data protection and compliance." />
          
          {/* Twitter */}
          <meta property="twitter:card" content="summary_large_image" />
          <meta property="twitter:url" content="https://teclara.tech/404" />
          <meta property="twitter:title" content="404 - Page Not Found | Teclara Technologies" />
          <meta property="twitter:description" content="Looking for enterprise-grade cybersecurity and IT support? Teclara Technologies provides comprehensive security solutions for Canadian businesses. Contact us at (************* or <EMAIL>" />
        </Helmet>
        
        <div className="container mx-auto px-4 max-w-4xl text-center">
          {/* Logo */}
          <div className="mb-8">
            <Link to="/">
              <img
                src="/uploads/teclara_logo_white_text.png"
                alt="Teclara Logo"
                className="h-12 mx-auto"
              />
            </Link>
          </div>

          {/* 404 Content */}
          <div className="mb-12">
            <h1 className="text-6xl font-bold mb-4 text-teclara-primary">404</h1>
            <h2 className="text-3xl font-semibold mb-4">Page Not Found</h2>
            <p className="text-xl text-gray-300 mb-8">
                 This page might've been encrypted, deleted, or teleported to a parallel network. ;)         </p>
            <Link 
              to="/" 
              className="inline-block bg-teclara-primary text-white px-8 py-3 rounded-lg hover:bg-teclara-primary-hover transition-colors"
            >
              Return to Home
            </Link>
          </div>

          {/* Contact Information */}
          <div className="border-t border-gray-700 pt-8">
            <h3 className="text-xl font-semibold mb-4">Need Help?</h3>
            <div className="flex flex-col md:flex-row justify-center items-center gap-6 text-gray-300">
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center gap-2 hover:text-teclara-primary transition-colors"
              >
                <Mail className="w-5 h-5" />
                <span><EMAIL></span>
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center gap-2 hover:text-teclara-primary transition-colors"
              >
                <Mail className="w-5 h-5" />
                <span><EMAIL></span>
              </a>
              <a 
                href="tel:+13659965856" 
                className="flex items-center gap-2 hover:text-teclara-primary transition-colors"
              >
                <Phone className="w-5 h-5" />
                <span>(*************</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NotFound;
