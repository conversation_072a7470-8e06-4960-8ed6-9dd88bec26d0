import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { ButtonLink } from '@/components/ui/button-link';
import { Shield, Lock, Cloud, Rocket, CheckCircle } from 'lucide-react';
import IndustrySelector from '@/components/IndustrySelector';
import CanonicalUrl from '@/components/CanonicalUrl';
import { Helmet } from 'react-helmet-async';

const StartupCaseStudy = () => {
  return (
    <>
      <Helmet>
        <title>Startup Cybersecurity Case Study | Teclara Technologies</title>
        <meta name="description" content="Startup cybersecurity case study: How <PERSON><PERSON><PERSON><PERSON> helped a growing startup implement scalable security solutions, protect sensitive data, and enable secure business growth." />
      </Helmet>
      <CanonicalUrl path="/case-studies/startup" />
      <div className="min-h-screen flex flex-col bg-white">
        <Navbar />
        
        {/* Industry Selector */}
        <section className="pt-24 pb-8 bg-[#F9FAFB]">
          <div className="container mx-auto px-4">
            <IndustrySelector currentIndustry="startup" />
          </div>
        </section>

        {/* Hero Section */}
        <section className="relative bg-white text-black overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white opacity-50"></div>
          <div className="container px-4 pt-12 pb-12 md:pt-20 md:pb-16 relative">
            <div className="flex flex-col items-center text-center max-w-4xl mx-auto space-y-8">
              <h1 className="font-bold tracking-tight text-black text-4xl md:text-5xl lg:text-6xl">
                How We Would Save Your Startup from <span style={{ color: '#FF1919' }}>Failure</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto text-center leading-relaxed">
                <strong>Fictional Scenario:</strong> A cyberattack destroys your startup's IP and scares away investors. Here's exactly how Teclara would prevent this business-killing disaster.
              </p>
            </div>
          </div>
        </section>

        {/* Implementation Approach */}
        <section className="pt-8 pb-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-16">
                {/* Assessment Phase */}
                <div className="space-y-6">
                  <h2 className="text-3xl font-bold text-center text-black">The Startup-Killing <span style={{ color: '#FF1919' }}>Attack</span> We Prevent</h2>
                  <div className="bg-red-50 rounded-xl p-8 border border-red-200">
                    <h3 className="text-2xl font-semibold mb-4 text-red-800">What Could Happen: The Dream Destroyer</h3>
                    <p className="text-sm text-red-600 mb-4 font-medium">*This is a fictional scenario based on real attacks that destroy startups*</p>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">IP Theft</p>
                          <p className="text-red-700 mt-1">Hackers could steal your innovative algorithms, business plans, and customer data. Competitors launch with your ideas before you do.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Investor Flight</p>
                          <p className="text-red-700 mt-1">News of a breach scares away investors. Funding rounds collapse, and your startup runs out of money within months.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Customer Exodus</p>
                          <p className="text-red-700 mt-1">Early adopters lose trust and abandon your platform. Without customers, your startup becomes worthless overnight.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Implementation Phase */}
                <div className="space-y-6">
                  <div className="bg-green-50 rounded-xl p-8 border border-green-200">
                    <h3 className="text-2xl font-semibold mb-4 text-green-800">How Teclara Would Protect Your Startup Dreams</h3>
                    <div className="grid md:grid-cols-2 gap-8">
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Shield className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Investor-Grade Security</p>
                            <p className="text-green-700 mt-1">Enterprise-level protection that impresses VCs and helps you close funding rounds faster.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Lock className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">IP Fortress</p>
                            <p className="text-green-700 mt-1">Military-grade encryption that keeps your innovations safe from competitors and hackers.</p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Cloud className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Scale Without Risk</p>
                            <p className="text-green-700 mt-1">Security that grows with you from 5 to 500 employees without slowing down innovation.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Rocket className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Enterprise Sales Ready</p>
                            <p className="text-green-700 mt-1">Security certifications that help you win Fortune 500 customers and charge premium prices.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Results & Benefits */}
                <div className="space-y-6">
                  <div className="bg-blue-50 rounded-xl p-8 border border-blue-200">
                    <h3 className="text-2xl font-semibold mb-4 text-blue-800">The Results: Your Startup Thrives & Scales</h3>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Investor Confidence</p>
                          <p className="text-blue-700 mt-1">Security certifications help you raise funding faster and at higher valuations. VCs trust you with their money.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Enterprise Customers</p>
                          <p className="text-blue-700 mt-1">Win Fortune 500 clients who demand enterprise-grade security. Charge premium prices for your solutions.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Competitive Advantage</p>
                          <p className="text-blue-700 mt-1">While competitors suffer breaches and lose customers, your startup becomes the trusted market leader.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-[#2a2a2a] text-white">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Don't Let Hackers <span style={{ color: '#FF1919' }}>Kill</span> Your Dreams</h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                Every day you wait is another day hackers could destroy your startup and steal your innovations. Get your free security assessment before you lose everything.
              </p>
              <ButtonLink
                className="bg-[#FF1919] hover:bg-[#D40000] text-white px-8 py-4 rounded font-bold text-xl shadow-lg transition-all duration-300 transform hover:scale-105"
                href="/Book"
                target="_blank"
              >
                Get FREE Security Assessment Now
              </ButtonLink>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default StartupCaseStudy; 