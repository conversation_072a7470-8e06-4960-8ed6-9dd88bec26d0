import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { ButtonLink } from '@/components/ui/button-link';
import { Shield, Lock, FileText, Users, CheckCircle } from 'lucide-react';
import IndustrySelector from '@/components/IndustrySelector';
import CanonicalUrl from '@/components/CanonicalUrl';
import { Helmet } from 'react-helmet-async';

const LawFirmCaseStudy = () => {
  return (
    <>
      <Helmet>
        <title>Law Firm Cybersecurity Case Study | Teclara Technologies</title>
        <meta name="description" content="Law firm cybersecurity case study: How Teclara implemented comprehensive security solutions to protect client confidentiality, ensure compliance, and prevent data breaches." />
      </Helmet>
      <CanonicalUrl path="/case-studies/law-firm" />
      <div className="min-h-screen flex flex-col bg-white">
        <Navbar />
        
        {/* Industry Selector */}
        <section className="pt-24 pb-8 bg-[#F9FAFB]">
          <div className="container mx-auto px-4">
            <IndustrySelector currentIndustry="law" />
          </div>
        </section>

        {/* Hero Section */}
        <section className="relative bg-white text-black overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white opacity-50"></div>
          <div className="container px-4 pt-12 pb-12 md:pt-20 md:pb-16 relative">
            <div className="flex flex-col items-center text-center max-w-4xl mx-auto space-y-8">
              <h1 className="font-bold tracking-tight text-black text-4xl md:text-5xl lg:text-6xl">
                How We Would Save Your Law Firm from <span style={{ color: '#FF1919' }}>Disaster</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto text-center leading-relaxed">
                <strong>Fictional Scenario:</strong> A ransomware attack threatens to destroy a 50-attorney firm. Here's exactly how Teclara would prevent this $2.3 million disaster and save the practice.
              </p>
            </div>
          </div>
        </section>

        {/* Implementation Approach */}
        <section className="pt-8 pb-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-16">
                {/* Assessment Phase */}
                <div className="space-y-6">
                  <h2 className="text-3xl font-bold text-center text-black">The Nightmare Scenario We <span style={{ color: '#FF1919' }}>Prevent</span></h2>
                  <div className="bg-red-50 rounded-xl p-8 border border-red-200">
                    <h3 className="text-2xl font-semibold mb-4 text-red-800">What Could Happen: The Attack Scenario</h3>
                    <p className="text-sm text-red-600 mb-4 font-medium">*This is a fictional scenario based on real attack patterns we see in the legal industry*</p>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Ransomware Infiltration</p>
                          <p className="text-red-700 mt-1">A single phishing email could infect an entire network. Within hours, 15,000 client files could be encrypted and held hostage for $500,000.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Client Data Exposed</p>
                          <p className="text-red-700 mt-1">Attorney-client privilege could be compromised. Sensitive case files, financial records, and personal information at risk of public exposure.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Business Shutdown</p>
                          <p className="text-red-700 mt-1">No access to files means missed court deadlines, client lawsuits, and potential bankruptcy. This happens to law firms every week.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Implementation Phase */}
                <div className="space-y-6">
                  <div className="bg-green-50 rounded-xl p-8 border border-green-200">
                    <h3 className="text-2xl font-semibold mb-4 text-green-800">How Teclara Would Prevent This Disaster</h3>
                    <div className="grid md:grid-cols-2 gap-8">
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Shield className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">PIPEDA-Compliant Encryption</p>
                            <p className="text-green-700 mt-1">Military-grade encryption that makes stolen data useless to hackers while keeping you compliant with regulations.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Lock className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Ransomware Immunity</p>
                            <p className="text-green-700 mt-1">AI-powered threat detection that stops ransomware before it can encrypt a single file. Your practice stays operational.</p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <FileText className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Secure Case Management</p>
                            <p className="text-green-700 mt-1">Court-admissible audit trails and secure document sharing that protects attorney-client privilege.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Users className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Human Firewall Training</p>
                            <p className="text-green-700 mt-1">Turn your staff into security experts who can spot and stop phishing attacks before they start.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Results & Benefits */}
                <div className="space-y-6">
                  <div className="bg-blue-50 rounded-xl p-8 border border-blue-200">
                    <h3 className="text-2xl font-semibold mb-4 text-blue-800">The Results: Your Practice Stays Protected</h3>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Zero Successful Attacks</p>
                          <p className="text-blue-700 mt-1">Our clients have never lost a single file to ransomware. Your practice continues operating while others shut down.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Client Trust Maintained</p>
                          <p className="text-blue-700 mt-1">PIPEDA compliance and security certifications help you win bigger clients and charge premium rates.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Sleep Peacefully</p>
                          <p className="text-blue-700 mt-1">24/7 monitoring means we catch threats before they become problems. Focus on law, not IT security.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-[#2a2a2a] text-white">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Don't Let This Happen to <span style={{ color: '#FF1919' }}>Your</span> Practice</h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                Every day you wait is another day hackers could destroy your life's work. Get your free security assessment before you become their next victim.
              </p>
              <ButtonLink
                className="bg-[#FF1919] hover:bg-[#D40000] text-white px-8 py-4 rounded font-bold text-xl shadow-lg transition-all duration-300 transform hover:scale-105"
                href="/Book"
                target="_blank"
              >
                Get FREE Security Assessment Now
              </ButtonLink>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default LawFirmCaseStudy; 