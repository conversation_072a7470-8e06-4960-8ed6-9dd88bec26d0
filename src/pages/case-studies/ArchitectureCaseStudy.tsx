import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { ButtonLink } from '@/components/ui/button-link';
import { Shield, HardDrive, Cloud, Lock, CheckCircle } from 'lucide-react';
import IndustrySelector from '@/components/IndustrySelector';
import CanonicalUrl from '@/components/CanonicalUrl';
import { Helmet } from 'react-helmet-async';
import { createCaseStudySchema } from '@/utils/schemaData';

const ArchitectureCaseStudy = () => {
  const caseStudySchema = createCaseStudySchema(
    "How We Protected an Architecture Firm's Design Assets with Enterprise Security",
    "Architecture firm cybersecurity success story: Securing design assets, enabling secure collaboration, and protecting intellectual property with enterprise-grade security solutions.",
    "https://teclara.tech/images/case-studies/architecture-preview.jpg",
    "https://teclara.tech/case-studies/architecture"
  );

  return (
    <>
      <Helmet>
        <title>Architecture Firm Cybersecurity Case Study | Teclara Technologies</title>
        <meta name="description" content="Architecture firm cybersecurity success story: Securing design assets, enabling secure collaboration, and protecting intellectual property with enterprise-grade security solutions." />
        
        {/* Page specific Open Graph */}
        <meta property="og:title" content="Architecture Firm Cybersecurity Case Study | Teclara Technologies" />
        <meta property="og:description" content="Learn how we helped an architecture firm secure their design assets and enable secure collaboration with enterprise-grade cybersecurity solutions." />
        <meta property="og:url" content="https://teclara.tech/case-studies/architecture" />
        <meta property="og:image" content="https://teclara.tech/images/case-studies/architecture-preview.jpg" />
        <meta property="og:type" content="article" />
        
        {/* Page specific Twitter */}
        <meta name="twitter:title" content="Architecture Firm Cybersecurity Case Study | Teclara Technologies" />
        <meta name="twitter:description" content="Learn how we helped an architecture firm secure their design assets and enable secure collaboration with enterprise-grade cybersecurity solutions." />
        <meta name="twitter:image" content="https://teclara.tech/images/case-studies/architecture-preview.jpg" />
        
        {/* Schema.org data */}
        <script type="application/ld+json">
          {JSON.stringify(caseStudySchema)}
        </script>
      </Helmet>
      <CanonicalUrl path="/case-studies/architecture" />
      <div className="min-h-screen flex flex-col bg-white">
        <Navbar />
        
        {/* Industry Selector */}
        <section className="pt-24 pb-8 bg-[#F9FAFB]">
          <div className="container mx-auto px-4">
            <IndustrySelector currentIndustry="architecture" />
          </div>
        </section>

        {/* Hero Section */}
        <section className="relative bg-white text-black overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white opacity-50"></div>
          <div className="container px-4 pt-12 pb-12 md:pt-20 md:pb-16 relative">
            <div className="flex flex-col items-center text-center max-w-4xl mx-auto space-y-8">
              <h1 className="font-bold tracking-tight text-black text-4xl md:text-5xl lg:text-6xl">
                How We Would Save Your Firm from <span style={{ color: '#FF1919' }}>Design Theft</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto text-center leading-relaxed">
                <strong>Fictional Scenario:</strong> Ransomware destroys years of architectural designs and competitors steal your concepts. Here's exactly how Teclara would prevent this creative catastrophe.
              </p>
            </div>
          </div>
        </section>

        {/* Implementation Approach */}
        <section className="pt-8 pb-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-16">
                {/* Assessment Phase */}
                <div className="space-y-6">
                  <h2 className="text-3xl font-bold text-center text-black">The Creative <span style={{ color: '#FF1919' }}>Nightmare</span> We Prevent</h2>
                  <div className="bg-red-50 rounded-xl p-8 border border-red-200">
                    <h3 className="text-2xl font-semibold mb-4 text-red-800">What Could Happen: The Design Disaster</h3>
                    <p className="text-sm text-red-600 mb-4 font-medium">*This is a fictional scenario based on real attacks targeting architecture firms*</p>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Years of Work Destroyed</p>
                          <p className="text-red-700 mt-1">Ransomware could encrypt every design file, CAD drawing, and project document. Decades of creative work held hostage for millions.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Design Theft</p>
                          <p className="text-red-700 mt-1">Competitors could steal your innovative concepts and win projects with your ideas. Your unique designs become their competitive advantage.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Project Delays</p>
                          <p className="text-red-700 mt-1">Unable to access files means missed deadlines, angry clients, and massive penalty fees. Your reputation in the industry is destroyed.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Implementation Phase */}
                <div className="space-y-6">
                  <div className="bg-green-50 rounded-xl p-8 border border-green-200">
                    <h3 className="text-2xl font-semibold mb-4 text-green-800">How Teclara Would Protect Your Creative Assets</h3>
                    <div className="grid md:grid-cols-2 gap-8">
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Shield className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Design Vault Security</p>
                            <p className="text-green-700 mt-1">Fort Knox-level protection for your CAD files and designs. Even if hackers get in, they can't access your creative work.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <HardDrive className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Instant Recovery</p>
                            <p className="text-green-700 mt-1">Ransomware-proof backups that restore your files in minutes, not weeks. Never lose a single design again.</p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Cloud className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Secure Client Portals</p>
                            <p className="text-green-700 mt-1">Share designs safely with clients and contractors without email risks. Professional presentation that wins more projects.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Lock className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Anti-Theft Protection</p>
                            <p className="text-green-700 mt-1">Watermarking and access controls that prevent competitors from stealing your innovative concepts and designs.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Results & Benefits */}
                <div className="space-y-6">
                  <div className="bg-blue-50 rounded-xl p-8 border border-blue-200">
                    <h3 className="text-2xl font-semibold mb-4 text-blue-800">The Results: Your Creative Legacy Stays Protected</h3>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Zero Design Loss</p>
                          <p className="text-blue-700 mt-1">Your creative work stays safe from ransomware and theft. Years of innovation protected forever.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Client Confidence</p>
                          <p className="text-blue-700 mt-1">Secure collaboration tools impress clients and help you win more high-value architectural projects.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Competitive Edge</p>
                          <p className="text-blue-700 mt-1">While competitors lose designs to attacks, your firm becomes known for reliability and security.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-[#2a2a2a] text-white">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Don't Let Hackers <span style={{ color: '#FF1919' }}>Steal</span> Your Designs</h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                Every day you wait is another day hackers could destroy years of creative work. Get your free security assessment before you lose everything.
              </p>
              <ButtonLink
                className="bg-[#FF1919] hover:bg-[#D40000] text-white px-8 py-4 rounded font-bold text-xl shadow-lg transition-all duration-300 transform hover:scale-105"
                href="/Book"
                target="_blank"
              >
                Get FREE Security Assessment Now
              </ButtonLink>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default ArchitectureCaseStudy;