import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { ButtonLink } from '@/components/ui/button-link';
import { Shield, Lock, Globe, FileText, CheckCircle } from 'lucide-react';
import IndustrySelector from '@/components/IndustrySelector';
import CanonicalUrl from '@/components/CanonicalUrl';
import { Helmet } from 'react-helmet-async';

const ConsultingCaseStudy = () => {
  return (
    <>
      <Helmet>
        <title>Consulting Firm Cybersecurity Case Study | Teclara Technologies</title>
        <meta name="description" content="Consulting firm cybersecurity case study: How Teclara protects client confidentiality, secures strategic data, and enables trusted collaboration for consulting firms." />
      </Helmet>
      <CanonicalUrl path="/case-studies/consulting" />
      <div className="min-h-screen flex flex-col bg-white">
        <Navbar />
        
        {/* Industry Selector */}
        <section className="pt-24 pb-8 bg-[#F9FAFB]">
          <div className="container mx-auto px-4">
            <IndustrySelector currentIndustry="consulting" />
          </div>
        </section>

        {/* Hero Section */}
        <section className="relative bg-white text-black overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white opacity-50"></div>
          <div className="container px-4 pt-12 pb-12 md:pt-20 md:pb-16 relative">
            <div className="flex flex-col items-center text-center max-w-4xl mx-auto space-y-8">
              <h1 className="font-bold tracking-tight text-black text-4xl md:text-5xl lg:text-6xl">
                How We Would Save Your Consulting Firm from <span style={{ color: '#FF1919' }}>Client Exodus</span>
              </h1>
              <p className="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto text-center leading-relaxed">
                <strong>Fictional Scenario:</strong> A data breach exposes your biggest client's strategic plans to competitors. Here's exactly how Teclara would prevent this reputation-destroying disaster.
              </p>
            </div>
          </div>
        </section>

        {/* Implementation Approach */}
        <section className="pt-8 pb-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-16">
                {/* Assessment Phase */}
                <div className="space-y-6">
                  <h2 className="text-3xl font-bold text-center text-black">The Trust-Destroying <span style={{ color: '#FF1919' }}>Breach</span> We Prevent</h2>
                  <div className="bg-red-50 rounded-xl p-8 border border-red-200">
                    <h3 className="text-2xl font-semibold mb-4 text-red-800">What Could Happen: The Reputation Killer</h3>
                    <p className="text-sm text-red-600 mb-4 font-medium">*This is a fictional scenario based on real attacks targeting consulting firms*</p>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Strategic Plans Stolen</p>
                          <p className="text-red-700 mt-1">Hackers could steal your client's merger plans, competitive strategies, and financial projections. Competitors get insider information worth millions.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Client Lawsuits</p>
                          <p className="text-red-700 mt-1">Breached clients sue for damages, lost opportunities, and competitive disadvantage. Legal fees alone could bankrupt your firm.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Industry Blacklist</p>
                          <p className="text-red-700 mt-1">Word spreads fast in consulting. No Fortune 500 company will trust you with sensitive projects ever again.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Implementation Phase */}
                <div className="space-y-6">
                  <div className="bg-green-50 rounded-xl p-8 border border-green-200">
                    <h3 className="text-2xl font-semibold mb-4 text-green-800">How Teclara Would Protect Your Client Secrets</h3>
                    <div className="grid md:grid-cols-2 gap-8">
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Shield className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Vault-Level Encryption</p>
                            <p className="text-green-700 mt-1">Military-grade encryption makes stolen data completely useless. Even if hackers get in, they can't read anything.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Lock className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Email Fortress</p>
                            <p className="text-green-700 mt-1">AI-powered email security stops 99.9% of phishing attacks before they reach your team's inbox.</p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Globe className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Secure Collaboration</p>
                            <p className="text-green-700 mt-1">Work from anywhere without exposing client data. Secure file sharing that clients actually trust.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <FileText className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Win Bigger Deals</p>
                            <p className="text-green-700 mt-1">Security certifications help you win Fortune 500 clients who demand enterprise-grade protection.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Results & Benefits */}
                <div className="space-y-6">
                  <div className="bg-blue-50 rounded-xl p-8 border border-blue-200">
                    <h3 className="text-2xl font-semibold mb-4 text-blue-800">The Results: Your Reputation Stays Bulletproof</h3>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Zero Data Breaches</p>
                          <p className="text-blue-700 mt-1">Your clients' secrets stay secret. No leaked strategies, no competitive disadvantage, no lawsuits.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Premium Client Trust</p>
                          <p className="text-blue-700 mt-1">Security certifications help you win Fortune 500 clients and charge premium rates for sensitive projects.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Competitive Advantage</p>
                          <p className="text-blue-700 mt-1">While competitors suffer breaches and lose clients, you become the trusted advisor everyone wants to work with.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-[#2a2a2a] text-white">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Don't Let Hackers <span style={{ color: '#FF1919' }}>Destroy</span> Your Reputation</h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                Every day you wait is another day hackers could steal your clients' most sensitive secrets. Get your free security assessment before you lose everything.
              </p>
              <ButtonLink
                className="bg-[#FF1919] hover:bg-[#D40000] text-white px-8 py-4 rounded font-bold text-xl shadow-lg transition-all duration-300 transform hover:scale-105"
                href="/Book"
                target="_blank"
              >
                Get FREE Security Assessment Now
              </ButtonLink>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default ConsultingCaseStudy; 