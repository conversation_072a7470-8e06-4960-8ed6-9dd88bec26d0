import React from 'react';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { ButtonLink } from '@/components/ui/button-link';
import { Shield, Lock, Clock, BarChart, CheckCircle } from 'lucide-react';
import IndustrySelector from '@/components/IndustrySelector';
import CanonicalUrl from '@/components/CanonicalUrl';
import { Helmet } from 'react-helmet-async';

const FinancialServicesCaseStudy = () => {
  return (
    <>
      <Helmet>
        <title>Financial Services Cybersecurity Case Study | Teclara Technologies</title>
        <meta name="description" content="Financial services cybersecurity case study: How Teclara protects banks and financial institutions from fraud, ensures regulatory compliance, and prevents costly breaches." />
      </Helmet>
      <CanonicalUrl path="/case-studies/financial-services" />
      <div className="min-h-screen flex flex-col bg-white">
        <Navbar />
        
        {/* Industry Selector */}
        <section className="pt-24 pb-8 bg-[#F9FAFB]">
          <div className="container mx-auto px-4">
            <IndustrySelector currentIndustry="finance" />
          </div>
        </section>

        {/* Hero Section */}
        <section className="relative bg-white text-black overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white opacity-50"></div>
          <div className="container px-4 pt-12 pb-12 md:pt-20 md:pb-16 relative">
            <div className="flex flex-col items-center text-center max-w-4xl mx-auto space-y-8">
              <h1 className="font-bold tracking-tight text-black text-4xl md:text-5xl lg:text-6xl">
                How We Would Save Your Bank from <span style={{ color: '#FF1919' }}>$18 Million</span> in Losses
              </h1>
              <p className="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto text-center leading-relaxed">
                <strong>Fictional Scenario:</strong> A cyberattack threatens to destroy a regional bank's reputation and trigger massive regulatory fines. Here's exactly how Teclara would prevent this financial catastrophe.
              </p>
            </div>
          </div>
        </section>

        {/* Implementation Approach */}
        <section className="pt-8 pb-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-16">
                {/* Assessment Phase */}
                <div className="space-y-6">
                  <h2 className="text-3xl font-bold text-center text-black">The Financial <span style={{ color: '#FF1919' }}>Nightmare</span> We Prevent</h2>
                  <div className="bg-red-50 rounded-xl p-8 border border-red-200">
                    <h3 className="text-2xl font-semibold mb-4 text-red-800">What Could Happen: The Attack Scenario</h3>
                    <p className="text-sm text-red-600 mb-4 font-medium">*This is a fictional scenario based on real attack patterns targeting financial institutions*</p>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Wire Fraud Attack</p>
                          <p className="text-red-700 mt-1">Hackers could intercept wire transfers, stealing millions before anyone notices. Customer accounts drained, trust destroyed forever.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Regulatory Shutdown</p>
                          <p className="text-red-700 mt-1">OSFI could impose $10+ million in fines and force closure. Non-compliance means losing your banking license permanently.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-red-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-red-800">Customer Exodus</p>
                          <p className="text-red-700 mt-1">News of a breach spreads instantly. Customers withdraw funds, stock price crashes, and competitors steal your market share.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>

                {/* Implementation Phase */}
                <div className="space-y-6">
                  <div className="bg-green-50 rounded-xl p-8 border border-green-200">
                    <h3 className="text-2xl font-semibold mb-4 text-green-800">How Teclara Would Prevent This Financial Disaster</h3>
                    <div className="grid md:grid-cols-2 gap-8">
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Shield className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">OSFI-Ready Compliance</p>
                            <p className="text-green-700 mt-1">Automated compliance monitoring that passes every audit and keeps regulators happy. No fines, no shutdowns.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Lock className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Fraud Detection AI</p>
                            <p className="text-green-700 mt-1">AI that spots fraudulent transactions in milliseconds, stopping wire fraud before a single dollar is stolen.</p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <Clock className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">99.99% Uptime Guarantee</p>
                            <p className="text-green-700 mt-1">Redundant systems ensure your bank never goes offline. Customers can always access their money.</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <BarChart className="h-6 w-6 text-green-600 mr-3 flex-shrink-0 mt-1" />
                          <div>
                            <p className="font-medium text-green-800">Fort Knox Security</p>
                            <p className="text-green-700 mt-1">Bank-grade encryption and multi-factor authentication that makes your systems impenetrable to hackers.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Results & Benefits */}
                <div className="space-y-6">
                  <div className="bg-blue-50 rounded-xl p-8 border border-blue-200">
                    <h3 className="text-2xl font-semibold mb-4 text-blue-800">The Results: Your Bank Stays Profitable & Protected</h3>
                    <ul className="space-y-4">
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Zero Fraud Losses</p>
                          <p className="text-blue-700 mt-1">Our AI stops 100% of wire fraud attempts. Your customers' money stays safe, and your reputation stays intact.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Regulatory Confidence</p>
                          <p className="text-blue-700 mt-1">Pass every OSFI audit with flying colors. No fines, no penalties, no regulatory headaches.</p>
                        </div>
                      </li>
                      <li className="flex items-start">
                        <CheckCircle className="h-6 w-6 text-blue-600 mr-3 flex-shrink-0 mt-1" />
                        <div>
                          <p className="font-medium text-blue-800">Customer Trust</p>
                          <p className="text-blue-700 mt-1">Security certifications help you attract high-value customers and charge premium rates for your services.</p>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-[#2a2a2a] text-white">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-2xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">Don't Let Hackers <span style={{ color: '#FF1919' }}>Bankrupt</span> Your Institution</h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                Every day you wait is another day hackers could steal millions and destroy your reputation. Get your free security assessment before regulators shut you down.
              </p>
              <ButtonLink
                className="bg-[#FF1919] hover:bg-[#D40000] text-white px-8 py-4 rounded font-bold text-xl shadow-lg transition-all duration-300 transform hover:scale-105"
                href="/Book"
                target="_blank"
              >
                Get FREE Security Assessment Now
              </ButtonLink>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default FinancialServicesCaseStudy; 