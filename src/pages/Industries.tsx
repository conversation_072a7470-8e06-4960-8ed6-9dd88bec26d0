import { motion } from "framer-motion";
import { useState } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Link } from "react-router-dom";
import {
  Shield,
  FileText,
  Lock,
  Users,
  Cloud,
  HardDrive,
  BarChart,
  Clock,
  Globe,
  Rocket,
} from "lucide-react";
import CanonicalUrl from "@/components/CanonicalUrl";
import { Helmet } from "react-helmet-async";
import {
  createServiceSchema,
  createLocalBusinessSchema,
  createFAQSchema,
} from "@/utils/schemaData";
import LeadGenerationButton from "@/components/LeadGenerationButton";

const Industries = () => {
  const [activeTab, setActiveTab] = useState("law");

  const industryOptions = [
    { value: "law", label: "Law Firms" },
    { value: "finance", label: "Financial Services" },
    { value: "consulting", label: "Consulting" },
    { value: "architecture", label: "Architecture" },
    { value: "startup", label: "Startups" },
  ];

  const industryFAQs = [
    {
      question: "How do you tailor solutions for different industries?",
      answer:
        "We customize our cybersecurity and IT solutions based on each industry's specific compliance requirements, risk profiles, and operational needs, ensuring optimal protection and efficiency.",
    },
    {
      question: "What compliance standards do you support?",
      answer:
        "We support various industry-specific compliance standards including PIPEDA, PCI DSS, ISO 27001, and industry-specific regulations for legal, financial, and healthcare sectors.",
    },
    {
      question: "Do you have experience with my industry?",
      answer:
        "Yes, we have extensive experience serving legal firms, financial services, consulting firms, architecture firms, and startups, with specialized solutions for each sector.",
    },
    {
      question: "How do you handle industry-specific security requirements?",
      answer:
        "We implement industry-specific security controls, conduct regular risk assessments, and maintain compliance with relevant regulations while providing ongoing monitoring and support.",
    },
  ];

  const localBusinessSchema = createLocalBusinessSchema({
    name: "Teclara Technologies",
    description:
      "Industry-specific cybersecurity and IT solutions for Canadian businesses",
    address: {
      street: "123 Business Street",
      city: "Oakville",
      region: "ON",
      postalCode: "L6H 0C3",
      country: "CA",
    },
    geo: {
      latitude: 43.4675,
      longitude: -79.6877,
    },
    phone: "******-123-4567",
    email: "<EMAIL>",
    priceRange: "$$",
    openingHours: [
      "Monday 09:00-17:00",
      "Tuesday 09:00-17:00",
      "Wednesday 09:00-17:00",
      "Thursday 09:00-17:00",
      "Friday 09:00-17:00",
    ],
  });

  const serviceSchema = createServiceSchema(
    "Industry-Specific IT & Security Solutions",
    "Tailored cybersecurity and IT solutions for legal, financial, consulting, and architectural firms. Industry-specific compliance, risk management, and security expertise.",
    "IT Services & Security",
    {
      areaServed: [
        "Oakville",
        "Hamilton",
        "Milton",
        "St. Catharines",
        "Halton Region",
      ],
      serviceOutput: [
        "Industry-Specific Compliance",
        "Risk Management",
        "Data Protection",
        "Secure Communication",
        "Document Management",
        "Client Data Security",
        "Regulatory Compliance",
        "Business Continuity",
        "Staff Training",
        "24/7 Support",
      ],
    }
  );

  const faqSchema = createFAQSchema(industryFAQs);

  return (
    <>
      <Helmet>
        <title>
          Industries We Serve in Oakville, Hamilton, Milton, St. Catharines &
          Halton Region | Teclara Technologies
        </title>
        <meta
          name="description"
          content="Specialized cybersecurity and IT solutions for legal firms, financial services, healthcare, and professional services. Industry compliance expertise and tailored security frameworks."
        />

        {/* Page specific Open Graph */}
        <meta
          property="og:title"
          content="Industry-Specific IT Solutions | Teclara Technologies"
        />
        <meta
          property="og:description"
          content="Specialized IT and security solutions for legal, financial, and professional services. Industry-specific compliance and risk management expertise."
        />
        <meta property="og:url" content="https://teclara.tech/industries" />
        <meta property="og:type" content="website" />
        <meta
          property="og:image"
          content="https://teclara.tech/uploads/teclara_logo_black_text.png"
        />
        <meta
          property="og:image:alt"
          content="Teclara Technologies Industry Solutions"
        />

        {/* Page specific Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta
          name="twitter:title"
          content="Industry-Specific IT Solutions | Teclara Technologies"
        />
        <meta
          name="twitter:description"
          content="Tailored cybersecurity and IT solutions for legal, financial, consulting, and architectural firms. Industry-specific compliance, risk management, and security expertise."
        />
        <meta
          name="twitter:image"
          content="https://teclara.tech/uploads/teclara_logo_black_text.png"
        />

        {/* Additional meta tags */}
        <meta
          name="keywords"
          content="industry-specific IT, legal IT solutions, financial services IT, consulting firm IT, architecture firm IT, compliance, risk management, data protection, Oakville, Hamilton, Milton, St. Catharines, Halton Region"
        />
        <meta name="author" content="Teclara Technologies" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="en-CA" />
        <meta name="geo.region" content="CA-ON" />
        <meta name="geo.placename" content="Oakville, Ontario" />
        <meta name="geo.position" content="43.4675;-79.6877" />
        <meta name="ICBM" content="43.4675, -79.6877" />

        {/* Resource hints */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />

        {/* Schema.org data */}
        <script type="application/ld+json">
          {JSON.stringify(localBusinessSchema)}
        </script>
        <script type="application/ld+json">
          {JSON.stringify(serviceSchema)}
        </script>
        <script type="application/ld+json">{JSON.stringify(faqSchema)}</script>
      </Helmet>
      <CanonicalUrl path="/industries" />

      <div className="min-h-screen bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] text-white">
        <Navbar />

        {/* Industries Tabs - Content Section */}
        <section className="pt-44 pb-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] relative overflow-hidden">
          {/* Brand Red Glow Background Effect - Subtle */}
          <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-[#FF1717]/20 rounded-full blur-3xl z-0" />

          {/* Background Grid Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `
                linear-gradient(rgba(52, 197, 182, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(52, 197, 182, 0.1) 1px, transparent 1px)
              `,
                backgroundSize: "50px 50px",
              }}
            />
          </div>

          <div className="max-w-[1280px] mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
                Industry-Specific{" "}
                <span className="text-[#6B8EF5]">Cybersecurity</span>
              </h1>
              <p className="text-xl text-gray-300 max-w-4xl mx-auto font-medium leading-relaxed mb-8">
                Every industry has unique security challenges. We deliver tailored cybersecurity and IT solutions that meet your specific compliance requirements, risk profiles, and operational needs.
              </p>
              <div className="flex justify-center mb-16">
                <LeadGenerationButton
                  formType="IT_CONSULT"
                  location="Industries Page - Header Section"
                  buttonText="Get Your Industry Assessment"
                  className="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
                />
              </div>
            </motion.div>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              {/* Mobile Dropdown */}
              <div className="flex justify-center mb-16 px-4 sm:hidden">
                <Select value={activeTab} onValueChange={setActiveTab}>
                  <SelectTrigger className="w-full max-w-xs bg-[#060d25]/80 backdrop-blur-sm border border-[#6B8EF5]/20 text-white">
                    <SelectValue placeholder="Select Industry" />
                  </SelectTrigger>
                  <SelectContent className="bg-[#060d25] border border-[#6B8EF5]/20">
                    {industryOptions.map((option) => (
                      <SelectItem
                        key={option.value}
                        value={option.value}
                        className="text-white hover:bg-[#6B8EF5]/20 focus:bg-[#6B8EF5]/20"
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Desktop Tabs */}
              <div className="hidden sm:flex justify-center mb-16 px-4">
                <div className="w-full max-w-6xl mx-auto">
                  <TabsList className="bg-[#060d25]/80 backdrop-blur-sm border border-[#6B8EF5]/20 p-1 rounded w-full flex justify-between">
                    <TabsTrigger
                      value="law"
                      className="rounded px-2 py-3 data-[state=active]:bg-[#6B8EF5] data-[state=active]:text-white text-gray-300 transition-all tracking-wide text-sm font-medium hover:bg-[#6B8EF5]/20 text-center flex-1 mx-0.5"
                    >
                      Law Firms
                    </TabsTrigger>
                    <TabsTrigger
                      value="finance"
                      className="rounded px-2 py-3 data-[state=active]:bg-[#6B8EF5] data-[state=active]:text-white text-gray-300 transition-all tracking-wide text-sm font-medium hover:bg-[#6B8EF5]/20 text-center flex-1 mx-0.5"
                    >
                      Financial Services
                    </TabsTrigger>
                    <TabsTrigger
                      value="consulting"
                      className="rounded px-2 py-3 data-[state=active]:bg-[#6B8EF5] data-[state=active]:text-white text-gray-300 transition-all tracking-wide text-sm font-medium hover:bg-[#6B8EF5]/20 text-center flex-1 mx-0.5"
                    >
                      Consulting
                    </TabsTrigger>
                    <TabsTrigger
                      value="architecture"
                      className="rounded px-2 py-3 data-[state=active]:bg-[#6B8EF5] data-[state=active]:text-white text-gray-300 transition-all tracking-wide text-sm font-medium hover:bg-[#6B8EF5]/20 text-center flex-1 mx-0.5"
                    >
                      Architecture
                    </TabsTrigger>
                    <TabsTrigger
                      value="startup"
                      className="rounded px-2 py-3 data-[state=active]:bg-[#6B8EF5] data-[state=active]:text-white text-gray-300 transition-all tracking-wide text-sm font-medium hover:bg-[#6B8EF5]/20 text-center flex-1 mx-0.5"
                    >
                      Startups
                    </TabsTrigger>
                  </TabsList>
                </div>
              </div>

              <TabsContent value="law" className="animate-fade-in">
                <div className="grid md:grid-cols-2 gap-12 items-start">
                  <div className="space-y-8">
                    <div className="space-y-6">
                      <h3 className="text-2xl md:text-3xl font-bold text-white tracking-wide">
                        One Breach Could{" "}
                        <span className="text-[#FF1717] drop-shadow-[0_0_8px_rgba(255,23,23,0.3)]">
                          End Your Practice
                        </span>
                      </h3>
                      <p className="text-lg text-gray-300 leading-relaxed font-medium">
                        Law firms are 3x more likely to be targeted by
                        cybercriminals than other businesses. A single data
                        breach can destroy client trust, trigger massive
                        lawsuits, and cost you your professional license. Don't
                        let hackers steal your life's work.
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Shield className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Pipeda Compliance
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Avoid $100K+ fines and protect attorney-client
                          privilege
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Lock className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Ransomware Shield
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Stop hackers from holding your case files hostage
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <FileText className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Secure Case Files
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Encrypted storage with complete audit trails for court
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Users className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Phishing Prevention
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Stop staff from accidentally exposing client data
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-[#060d25]/80 backdrop-blur-sm rounded p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300 h-full flex flex-col">
                    <h4 className="text-xl font-bold mb-4 text-white tracking-wide">
                      Example: Law Firm Security
                    </h4>
                    <p className="text-gray-300 mb-6 leading-relaxed flex-grow font-medium">
                      A mid-sized law firm with 50+ attorneys faced challenges
                      with client data protection and PIPEDA compliance. We
                      implemented a comprehensive security solution that
                      included end-to-end encryption for all client
                      communications, secure document management with granular
                      access controls, and automated compliance monitoring. The
                      firm now maintains full PIPEDA compliance while enabling
                      secure remote work for all staff. Regular security
                      training sessions have reduced phishing attempts by 95%,
                      and the new document management system has streamlined
                      client file sharing while maintaining strict
                      confidentiality.
                    </p>
                    <Link
                      to="/case-studies/law-firm"
                      className="inline-block mt-auto"
                    >
                      <Button className="w-full bg-[#6B8EF5] hover:bg-[#5B7FE8] text-white text-lg font-semibold py-6 tracking-wide transition-all duration-300 hover:shadow-[0_0_20px_rgba(254,196,0,0.5)]">
                        View Example
                      </Button>
                    </Link>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="finance" className="animate-fade-in">
                <div className="grid md:grid-cols-2 gap-12 items-start">
                  <div className="space-y-8">
                    <div className="space-y-6">
                      <h3 className="text-2xl md:text-3xl font-bold text-white tracking-wide">
                        Hackers Want Your{" "}
                        <span className="text-[#FF1717] drop-shadow-[0_0_8px_rgba(255,23,23,0.3)]">Money</span>
                      </h3>
                      <p className="text-lg text-gray-300 leading-relaxed font-medium">
                        Financial firms lose $18.3 million per breach on
                        average. Regulators will shut you down, customers will
                        sue you, and your reputation will be destroyed. One
                        successful attack could bankrupt your business
                        overnight.
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Shield className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Regulatory Compliance
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Pass OSFI audits and avoid million-dollar penalties
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Lock className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Fraud Detection
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Stop wire fraud before it bankrupts your clients
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Clock className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          99.99% Uptime
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Never lose money due to system downtime again
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <BarChart className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Secure Transactions
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Protect every dollar with bank-grade encryption
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-[#060d25]/80 backdrop-blur-sm rounded p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300 h-full flex flex-col">
                    <h4 className="text-xl font-bold mb-4 text-white tracking-wide">
                      Example: Financial Services
                    </h4>
                    <p className="text-gray-300 mb-6 leading-relaxed flex-grow font-medium">
                      A regional financial institution needed to meet strict
                      OSFI and PCI DSS requirements while modernizing their
                      security infrastructure. We implemented a multi-layered
                      security solution featuring redundant systems achieving
                      99.99% uptime, advanced fraud detection systems that
                      reduced fraudulent transactions by 85%, and a secure
                      customer portal with multi-factor authentication. The
                      institution now maintains full compliance while providing
                      enhanced security for customer transactions. The new
                      infrastructure has also reduced operational costs by 30%
                      through automation and improved efficiency.
                    </p>
                    <Link
                      to="/case-studies/financial-services"
                      className="inline-block mt-auto"
                    >
                      <Button className="w-full bg-[#6B8EF5] hover:bg-[#5B7FE8] text-white text-lg font-semibold py-6 tracking-wide transition-all duration-300 hover:shadow-[0_0_20px_rgba(254,196,0,0.5)]">
                        View Example
                      </Button>
                    </Link>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="consulting" className="animate-fade-in">
                <div className="grid md:grid-cols-2 gap-12 items-start">
                  <div className="space-y-8">
                    <div className="space-y-6">
                      <h3 className="text-2xl md:text-3xl font-bold text-white tracking-wide">
                        Your Clients'{" "}
                        <span className="text-[#FF1717] drop-shadow-[0_0_8px_rgba(255,23,23,0.3)]">
                          Secrets
                        </span>{" "}
                        Are at Risk
                      </h3>
                      <p className="text-lg text-gray-300 leading-relaxed font-medium">
                        Consulting firms hold the keys to their clients' most
                        sensitive strategies and data. One breach exposes
                        everything—competitive intelligence, financial data,
                        strategic plans. Your clients will never trust you
                        again, and word spreads fast in your industry.
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Shield className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Client Confidentiality
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Protect strategic plans worth millions to your clients
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Lock className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Email Security
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Stop hackers from intercepting sensitive
                          communications
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Globe className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Secure Collaboration
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Work remotely without exposing client secrets
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <FileText className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Win More Clients
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Security certifications that close bigger deals
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-[#060d25]/80 backdrop-blur-sm rounded p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300 h-full flex flex-col">
                    <h4 className="text-xl font-bold mb-4 text-white tracking-wide">
                      Example: Consulting Firm
                    </h4>
                    <p className="text-gray-300 mb-6 leading-relaxed flex-grow font-medium">
                      A global consulting firm with 500+ employees across
                      multiple jurisdictions needed to secure their remote
                      workforce while protecting sensitive client data. We
                      implemented a comprehensive security solution that
                      included secure remote access tools, encrypted
                      communication channels, and cloud-based collaboration
                      platforms. The firm now maintains consistent security
                      standards across all offices while enabling seamless
                      collaboration between teams. Client data is protected
                      through advanced encryption and access controls, and the
                      new security policies have helped the firm win several
                      high-profile clients requiring strict security standards.
                    </p>
                    <Link
                      to="/case-studies/consulting"
                      className="inline-block mt-auto"
                    >
                      <Button className="w-full bg-[#6B8EF5] hover:bg-[#5B7FE8] text-white text-lg font-semibold py-6 tracking-wide transition-all duration-300 hover:shadow-[0_0_20px_rgba(254,196,0,0.5)]">
                        View Example
                      </Button>
                    </Link>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="architecture" className="animate-fade-in">
                <div className="grid md:grid-cols-2 gap-12 items-start">
                  <div className="space-y-8">
                    <div className="space-y-6">
                      <h3 className="text-2xl md:text-3xl font-bold text-white tracking-wide">
                        Years of Work{" "}
                        <span className="text-[#FF1717] drop-shadow-[0_0_8px_rgba(255,23,23,0.3)]">Gone</span>{" "}
                        in Minutes
                      </h3>
                      <p className="text-lg text-gray-300 leading-relaxed font-medium">
                        Ransomware can destroy years of architectural designs in
                        minutes. Your intellectual property, client projects,
                        and creative work—all held hostage. Competitors could
                        steal your designs, clients could sue for delays, and
                        your reputation could be ruined forever.
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Shield className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Design Vault
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Protect years of creative work from theft and loss
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <HardDrive className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Ransomware Recovery
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Get your files back in hours, not weeks
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Cloud className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Client Portals
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Share designs safely without email risks
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Lock className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Stop Design Theft
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Prevent competitors from stealing your concepts
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-[#060d25]/80 backdrop-blur-sm rounded p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300 h-full flex flex-col">
                    <h4 className="text-xl font-bold mb-4 text-white tracking-wide">
                      Example: Architecture Firm
                    </h4>
                    <p className="text-gray-300 mb-6 leading-relaxed flex-grow font-medium">
                      A leading architecture firm was struggling to protect
                      their valuable design IP and manage secure collaboration
                      with clients. We implemented a comprehensive security
                      solution that included secure file sharing with version
                      control, client collaboration portals, and robust
                      ransomware protection. The firm now securely manages large
                      design files while maintaining strict access controls. The
                      new system has improved project delivery times by 40%
                      through streamlined collaboration, and the backup solution
                      ensures zero data loss in case of emergencies. Client
                      feedback has been overwhelmingly positive about the new
                      secure sharing capabilities.
                    </p>
                    <Link
                      to="/case-studies/architecture"
                      className="inline-block mt-auto"
                    >
                      <Button className="w-full bg-[#6B8EF5] hover:bg-[#5B7FE8] text-white text-lg font-semibold py-6 tracking-wide transition-all duration-300 hover:shadow-[0_0_20px_rgba(254,196,0,0.5)]">
                        View Example
                      </Button>
                    </Link>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="startup" className="animate-fade-in">
                <div className="grid md:grid-cols-2 gap-12 items-start">
                  <div className="space-y-8">
                    <div className="space-y-6">
                      <h3 className="text-2xl md:text-3xl font-bold text-white tracking-wide">
                        Don't Let Hackers{" "}
                        <span className="text-[#FF1717] drop-shadow-[0_0_8px_rgba(255,23,23,0.3)]">Kill</span>{" "}
                        Your Startup
                      </h3>
                      <p className="text-lg text-gray-300 leading-relaxed font-medium">
                        90% of startups that suffer a major cyberattack go out
                        of business within 6 months. Your investors will pull
                        funding, customers will flee, and your innovative ideas
                        could be stolen by competitors. Don't let poor security
                        destroy your dreams.
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Shield className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Investor Confidence
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Security that helps you raise funding and close deals
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Lock className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Protect Your IP
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Stop competitors from stealing your innovations
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Cloud className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Scale Securely
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Grow fast without security becoming a bottleneck
                        </p>
                      </div>
                      <div className="bg-[#060d25]/80 backdrop-blur-sm p-6 rounded border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300">
                        <Rocket className="h-8 w-8 text-[#6B8EF5] mb-4" />
                        <h4 className="font-semibold mb-2 text-white tracking-wide text-sm">
                          Enterprise Ready
                        </h4>
                        <p className="text-gray-300 font-medium">
                          Win big clients with enterprise-grade security
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-[#060d25]/80 backdrop-blur-sm rounded p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300 h-full flex flex-col">
                    <h4 className="text-xl font-bold mb-4 text-white tracking-wide">
                      Example: Tech Startup
                    </h4>
                    <p className="text-gray-300 mb-6 leading-relaxed flex-grow font-medium">
                      A fast-growing tech startup needed to build security into
                      their infrastructure from the ground up. We implemented a
                      comprehensive security solution that included secure cloud
                      infrastructure, DevSecOps practices, and scalable security
                      controls. The startup now maintains enterprise-grade
                      security while enabling rapid development and deployment.
                      The security-first approach has helped them win enterprise
                      clients and pass rigorous security audits. The automated
                      security controls have reduced manual security tasks by
                      70%, allowing the team to focus on innovation while
                      maintaining robust security standards.
                    </p>
                    <Link
                      to="/case-studies/startup"
                      className="inline-block mt-auto"
                    >
                      <Button className="w-full bg-[#6B8EF5] hover:bg-[#5B7FE8] text-white text-lg font-semibold py-6 tracking-wide transition-all duration-300 hover:shadow-[0_0_20px_rgba(254,196,0,0.5)]">
                        View Example
                      </Button>
                    </Link>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </section>

        {/* CTA Section - Secondary Section */}
        <section className="py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
          <div className="max-w-[1280px] mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white tracking-wide">
                Don't Wait Until It's{" "}
                <span className="text-[#FF1717] drop-shadow-[0_0_8px_rgba(255,23,23,0.3)]">Too Late</span>
              </h2>
              <p className="text-xl text-gray-300 leading-relaxed max-w-4xl mx-auto font-medium">
                Every day you wait is another day hackers could destroy your
                business. Get your free industry-specific security assessment
                before you become their next victim.
              </p>
              <LeadGenerationButton
                formType="IT_CONSULT"
                location="Industries Page CTA Section"
                buttonText="Schedule Your Free Consultation"
                className="bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]"
              />
            </motion.div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default Industries;
