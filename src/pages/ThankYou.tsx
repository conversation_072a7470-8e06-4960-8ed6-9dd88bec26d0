import React from 'react';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { CheckCircle } from 'lucide-react';
import CanonicalUrl from '@/components/CanonicalUrl';

const ThankYou = () => {
  return (
    <>
      <Helmet>
        <title>Thank You | Teclara Technologies</title>
        <meta name="description" content="Thank you for contacting Teclara Technologies. We've received your message and will respond within 24 hours." />
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>
      <CanonicalUrl path="/thank-you" />
      <div className="min-h-screen flex flex-col">
        <Navbar />
        
        {/* Thank You Section */}
        <section className="relative bg-white text-black flex-grow flex items-center">
          <div className="container px-4 py-20">
            <div className="flex flex-col items-center text-center max-w-2xl mx-auto space-y-8">
              <div className="h-20 w-20 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>
              
              <h1 className="font-bold tracking-tight text-4xl">
                Thank You for <span className="text-teclara-primary">Reaching Out</span>
              </h1>
              
              <p className="text-lg text-gray-700">
                We've received your message and will get back to you shortly. Our team typically responds within 24 hours during business days.
              </p>

              <div className="flex flex-wrap gap-4 justify-center mt-8">
                <Link 
                  to="/"
                  className="inline-flex items-center justify-center bg-teclara-primary hover:bg-teclara-primary-hover text-white px-6 py-3 text-base font-medium rounded-md transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Return Home
                </Link>
                <Link 
                  to="/contact"
                  className="inline-flex items-center justify-center bg-gray-800 hover:bg-gray-900 text-white px-6 py-3 text-base font-medium rounded-md transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Send Another Message
                </Link>
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  );
};

export default ThankYou; 