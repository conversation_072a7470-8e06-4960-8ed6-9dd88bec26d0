/**
 * Teclara Technologies - Centralized Color Theme System
 *
 * This file contains all color definitions for the application, organized by:
 * - Brand colors (primary, secondary, accent)
 * - Semantic colors (success, warning, error, info)
 * - Neutral colors (backgrounds, text, borders)
 * - Contextual colors (threat levels, security states)
 *
 * All colors support both light and dark modes with proper fallbacks.
 */

// Brand Core Colors - Based on Brand Guidelines
export const brandColors = {
  // Primary Brand Colors
  primary: {
    red: "#D14019", // Primary Red (main logo/icon color)
    crimson: "#CC1414", // Crimson Depth (hover/emphasis variant)
    oxide: "#A31212", // Oxide Red (deep/system variant)
    navy: "#14213D", // Primary Navy (supporting accent)
    gunmetal: "#5C5F63", // Gunmetal Gray (muted/passive/disabled)
  },

  // New Design System Colors - Updated Brief
  newDesign: {
    deepNavy: "#060d25", // Base color for backgrounds and text
    royalBlue: "#6B8EF5", // Primary accent for buttons, icons, hover effects (updated from turquoise)
    royalBlueHover: "#5B7FE8", // Hover state for royal blue
    brandRed: "#FF1717", // Strategic accent for alerts, warnings, and critical elements
    yellow: "#FEC400", // Button glow/shadow highlights
    darkNavy: "#04081a", // Darker navy for gradients
    white: "#FFFFFF", // Background color
    lightGray: "#F8F9FA", // Soft background variant
    // Legacy color for backward compatibility (deprecated)
    turquoise: "#6B8EF5", // Redirected to royal blue for compatibility
  },

  // Background Colors
  background: {
    light: "#F8F9FA", // Light Mode Background
    dark: "#14213D", // Dark Mode Background
  },

  // Extended Brand Palette
  extended: {
    navyLight: "#1A2A4A", // Lighter navy variant
    navyDark: "#0F1829", // Darker navy variant
    white: "#FFFFFF", // Pure white
    black: "#000000", // Pure black
  },
} as const;

// Semantic Colors - For UI States and Feedback
export const semanticColors = {
  success: {
    light: "#10B981", // Green for success states
    dark: "#059669", // Darker green for dark mode
    bg: "#ECFDF5", // Light green background
    text: "#065F46", // Dark green text
  },

  warning: {
    light: "#F59E0B", // Amber for warning states
    dark: "#D97706", // Darker amber for dark mode
    bg: "#FFFBEB", // Light amber background
    text: "#92400E", // Dark amber text
  },

  error: {
    light: "#EF4444", // Red for error states
    dark: "#DC2626", // Darker red for dark mode
    bg: "#FEF2F2", // Light red background
    text: "#991B1B", // Dark red text
  },

  info: {
    light: "#3B82F6", // Blue for info states
    dark: "#2563EB", // Darker blue for dark mode
    bg: "#EFF6FF", // Light blue background
    text: "#1E40AF", // Dark blue text
  },
} as const;

// Neutral Colors - For Text, Backgrounds, and Borders
export const neutralColors = {
  // Gray Scale
  gray: {
    50: "#F9FAFB",
    100: "#F3F4F6",
    200: "#E5E7EB",
    300: "#D1D5DB",
    400: "#9CA3AF",
    500: "#6B7280",
    600: "#4B5563",
    700: "#374151",
    800: "#1F2937",
    900: "#111827",
    950: "#030712",
  },

  // Text Colors
  text: {
    primary: {
      light: "#111827", // Dark text for light mode
      dark: "#F9FAFB", // Light text for dark mode
    },
    secondary: {
      light: "#6B7280", // Muted text for light mode
      dark: "#9CA3AF", // Muted text for dark mode
    },
    tertiary: {
      light: "#9CA3AF", // Very muted text for light mode
      dark: "#6B7280", // Very muted text for dark mode
    },
  },

  // Border Colors
  border: {
    light: "#E5E7EB", // Light border
    dark: "#374151", // Dark border
    muted: "#F3F4F6", // Very light border
  },
} as const;

// Contextual Colors - For Cybersecurity and Business Context
export const contextualColors = {
  // Threat Levels
  threat: {
    critical: "#D14019", // Critical threats (brand red)
    high: "#CC1414", // High threats (crimson)
    medium: "#A31212", // Medium threats (oxide red)
    low: "#5C5F63", // Low threats (gunmetal)
  },

  // Security States
  security: {
    secure: "#10B981", // Secure state (green)
    scanning: "#F59E0B", // Scanning state (amber)
    vulnerable: "#D14019", // Vulnerable state (brand red)
    unknown: "#5C5F63", // Unknown state (gunmetal)
  },

  // Business States
  business: {
    active: "#10B981", // Active/online
    inactive: "#6B7280", // Inactive/offline
    pending: "#F59E0B", // Pending/processing
    failed: "#EF4444", // Failed/error
  },
} as const;

// Theme Variants - Complete color schemes for different modes
export const themeVariants = {
  light: {
    // Backgrounds
    background: {
      primary: brandColors.background.light,
      secondary: brandColors.background.light,
      tertiary: neutralColors.gray[50],
      accent: neutralColors.gray[100],
    },

    // Text
    text: {
      primary: brandColors.primary.navy,
      secondary: neutralColors.text.secondary.light,
      tertiary: neutralColors.text.tertiary.light,
      inverse: "#FFFFFF",
    },

    // Brand
    brand: {
      primary: brandColors.primary.red,
      secondary: brandColors.primary.navy,
      accent: brandColors.primary.crimson,
      muted: brandColors.primary.gunmetal,
    },

    // Interactive
    interactive: {
      primary: brandColors.primary.red,
      primaryHover: brandColors.primary.crimson,
      secondary: brandColors.primary.navy,
      secondaryHover: "#1A2A4A",
      muted: neutralColors.gray[200],
      mutedHover: neutralColors.gray[300],
    },

    // Borders
    border: {
      primary: neutralColors.border.light,
      secondary: neutralColors.border.muted,
      accent: brandColors.primary.red,
    },
  },

  dark: {
    // Backgrounds
    background: {
      primary: brandColors.background.dark,
      secondary: "#1A2A4A",
      tertiary: "#0F1829",
      accent: neutralColors.gray[800],
    },

    // Text
    text: {
      primary: neutralColors.text.primary.dark,
      secondary: neutralColors.text.secondary.dark,
      tertiary: neutralColors.text.tertiary.dark,
      inverse: "#111827",
    },

    // Brand
    brand: {
      primary: brandColors.primary.red,
      secondary: brandColors.primary.navy,
      accent: brandColors.primary.crimson,
      muted: brandColors.primary.gunmetal,
    },

    // Interactive
    interactive: {
      primary: brandColors.primary.red,
      primaryHover: brandColors.primary.crimson,
      secondary: brandColors.primary.red,
      secondaryHover: brandColors.primary.crimson,
      muted: neutralColors.gray[700],
      mutedHover: neutralColors.gray[600],
    },

    // Borders
    border: {
      primary: neutralColors.border.dark,
      secondary: neutralColors.gray[700],
      accent: brandColors.primary.red,
    },
  },
} as const;

// Utility Functions for Color Management
export const colorUtils = {
  /**
   * Get color with opacity
   */
  withOpacity: (color: string, opacity: number): string => {
    // Handle hex colors
    if (color.startsWith("#")) {
      const hex = color.slice(1);
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }

    // Handle rgb/rgba colors
    if (color.startsWith("rgb")) {
      return color.replace("rgb(", "rgba(").replace(")", `, ${opacity})`);
    }

    return color;
  },

  /**
   * Get theme-aware color
   */
  getThemeColor: (colorPath: string, theme: "light" | "dark" = "light") => {
    const variant = themeVariants[theme];
    const keys = colorPath.split(".");
    let result: unknown = variant;

    for (const key of keys) {
      result = (result as Record<string, unknown>)?.[key];
    }

    return (result as string) || colorPath;
  },

  /**
   * Generate CSS custom properties for theme
   */
  generateCSSProperties: (theme: "light" | "dark" = "light") => {
    const variant = themeVariants[theme];
    const properties: Record<string, string> = {};

    const flatten = (obj: Record<string, unknown>, prefix = "") => {
      Object.keys(obj).forEach((key) => {
        const value = obj[key];
        const newKey = prefix ? `${prefix}-${key}` : key;

        if (typeof value === "object" && value !== null) {
          flatten(value as Record<string, unknown>, newKey);
        } else {
          properties[`--${newKey}`] = value as string;
        }
      });
    };

    flatten(variant);
    return properties;
  },
} as const;

// Export all color collections
export const colors = {
  brand: brandColors,
  semantic: semanticColors,
  neutral: neutralColors,
  contextual: contextualColors,
  themes: themeVariants,
  utils: colorUtils,
} as const;

// Type definitions for better TypeScript support
export type BrandColor = keyof typeof brandColors.primary;
export type SemanticColor = keyof typeof semanticColors;
export type NeutralColor = keyof typeof neutralColors.gray;
export type ContextualColor = keyof typeof contextualColors.threat;
export type ThemeVariant = keyof typeof themeVariants;

export default colors;
