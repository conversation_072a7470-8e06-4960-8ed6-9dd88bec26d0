import React, { createContext, useContext, useEffect } from 'react';
import { colors, colorUtils, ThemeVariant } from './colors';

interface ThemeContextType {
  theme: ThemeVariant;
  colors: typeof colors;
  getColor: (path: string) => string;
  withOpacity: (color: string, opacity: number) => string;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: ThemeVariant;
  // storageKey?: string; // No longer needed as we are removing theme switching
}

export function ThemeProvider({
  children,
  defaultTheme = 'light',
  // storageKey = 'teclara-theme', // No longer needed
}: ThemeProviderProps) {
  // Theme is always the default, no switching functionality
  const theme = defaultTheme;

  // Remove setTheme and toggleTheme as theme switching is disabled
  // const setTheme = (newTheme: ThemeVariant) => {
  //   setThemeState(newTheme);
  //   if (typeof window !== 'undefined') {
  //     localStorage.setItem(storageKey, newTheme);
  //   }
  // };

  // const toggleTheme = () => {
  //   setTheme(theme === 'light' ? 'dark' : 'light');
  // };

  const getColor = (path: string): string => {
    return colorUtils.getThemeColor(path, theme);
  };

  const withOpacity = (color: string, opacity: number): string => {
    return colorUtils.withOpacity(color, opacity);
  };

  // Apply theme to document root
  useEffect(() => {
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark');
    
    // Add current theme class
    root.classList.add(theme);
    
    // Generate and apply CSS custom properties
    const properties = colorUtils.generateCSSProperties(theme);
    Object.entries(properties).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
    
    // Set theme-color meta tag
    const themeColorMeta = document.querySelector('meta[name="theme-color"]');
    if (themeColorMeta) {
      themeColorMeta.setAttribute('content', colors.themes[theme].background.primary);
    }
  }, [theme]);

  // Commenting out system theme change listener as it overrides defaultTheme
  // useEffect(() => {
  //   if (typeof window === 'undefined') return;

  //   const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  //   const handleChange = (e: MediaQueryListEvent) => {
  //     const stored = localStorage.getItem(storageKey);
  //     if (!stored) {
  //       setThemeState(e.matches ? 'dark' : 'light');
  //     }
  //   };

  //   mediaQuery.addEventListener('change', handleChange);
  //   return () => mediaQuery.removeEventListener('change', handleChange);
  // }, [storageKey]);

  const contextValue: ThemeContextType = {
    theme,
    colors,
    getColor,
    withOpacity,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook for getting theme-aware colors
export function useThemeColors() {
  const { theme, colors } = useTheme();
  return colors.themes[theme];
}

// Hook for getting brand colors
export function useBrandColors() {
  const { colors } = useTheme();
  return colors.brand;
}

// Hook for getting semantic colors
export function useSemanticColors() {
  const { colors } = useTheme();
  return colors.semantic;
}

// Hook for getting contextual colors (threat levels, security states)
export function useContextualColors() {
  const { colors } = useTheme();
  return colors.contextual;
}

export default ThemeProvider;