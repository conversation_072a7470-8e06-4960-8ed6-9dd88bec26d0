import React, { lazy, Suspense, useEffect } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "@/theme/ThemeProvider";
import { HelmetProvider } from 'react-helmet-async';
import ScrollToTop from "./components/ScrollToTop";
import GlobalMetaTags from "./components/GlobalMetaTags";
import ClarityComponent from "./components/Clarity";

// Lazy load main pages
const Index = lazy(() => import("./pages/Index"));
const Landing = lazy(() => import("./pages/landing/index"));
const Pricing = lazy(() => import("./pages/Pricing"));
const Industries = lazy(() => import("./pages/Industries"));
const Contact = lazy(() => import("./pages/Contact"));
const ThankYou = lazy(() => import("./pages/ThankYou"));
const About = lazy(() => import("./pages/About"));
const Solutions = lazy(() => import("./pages/Solutions"));
const PrivacyPolicy = lazy(() => import("./pages/PrivacyPolicy"));
const TermsOfService = lazy(() => import("./pages/TermsOfService"));
const NotFound = lazy(() => import("./pages/NotFound"));
const ThreatMap = lazy(() => import("./pages/ThreatMap"));
const Book = lazy(() => import("./pages/Book"));
const Sitemap = lazy(() => import("./pages/Sitemap"));


// Lazy load landing pages
const CybersecurityLanding = lazy(() => import("./pages/landing/cybersecurity"));
const ManagedITLanding = lazy(() => import("./pages/landing/managed-it"));
const ContinuityLanding = lazy(() => import("./pages/landing/continuity"));

// Lazy load case studies
const LawFirmCaseStudy = lazy(() => import("./pages/case-studies/LawFirmCaseStudy"));
const FinancialServicesCaseStudy = lazy(() => import("./pages/case-studies/FinancialServicesCaseStudy"));
const ConsultingCaseStudy = lazy(() => import("./pages/case-studies/ConsultingCaseStudy"));
const ArchitectureCaseStudy = lazy(() => import("./pages/case-studies/ArchitectureCaseStudy"));
const StartupCaseStudy = lazy(() => import("./pages/case-studies/StartupCaseStudy"));

// Loading component with improved styling
const LoadingFallback = () => (
  <div className="min-h-screen bg-background flex items-center justify-center">
    <div className="flex flex-col items-center space-y-4">
      <div className="w-12 h-12 border-4 border-teclara-primary border-t-transparent rounded-full animate-spin"></div>
      <div className="text-foreground text-lg">Loading...</div>
    </div>
  </div>
);

// Create a wrapper component for Suspense to reduce repetition
const SuspenseWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingFallback />}>
    {children}
  </Suspense>
);

// Initialize QueryClient with default options
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (replaces cacheTime)
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const App = () => {
  // Font loading detection to prevent FOUC
  useEffect(() => {
    const checkFontsLoaded = () => {
      if (document.fonts && document.fonts.ready) {
        document.fonts.ready.then(() => {
          document.documentElement.classList.add('fonts-loaded');
        });
      } else {
        // Fallback for browsers without Font Loading API
        setTimeout(() => {
          document.documentElement.classList.add('fonts-loaded');
        }, 100);
      }
    };

    checkFontsLoaded();
  }, []);

  return (
    <HelmetProvider>
      <ThemeProvider defaultTheme="light" storageKey="teclara-theme">
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <GlobalMetaTags />
            <ClarityComponent
              projectId={import.meta.env.VITE_CLARITY_PROJECT_ID || "s98w795xn7"}
              enabled={true}
            />
            <Toaster />
            <Sonner />
            <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <ScrollToTop />
            <Routes>
              {/* Main Routes */}
              <Route path="/" element={<SuspenseWrapper><Index /></SuspenseWrapper>} />
              <Route path="/landing" element={<SuspenseWrapper><Landing /></SuspenseWrapper>} />
              <Route path="/pricing" element={<SuspenseWrapper><Pricing /></SuspenseWrapper>} />
              <Route path="/industries" element={<SuspenseWrapper><Industries /></SuspenseWrapper>} />
              <Route path="/contact" element={<SuspenseWrapper><Contact /></SuspenseWrapper>} />
              <Route path="/thank-you" element={<SuspenseWrapper><ThankYou /></SuspenseWrapper>} />
              <Route path="/about" element={<SuspenseWrapper><About /></SuspenseWrapper>} />
              <Route path="/solutions" element={<SuspenseWrapper><Solutions /></SuspenseWrapper>} />
              <Route path="/threatmap" element={<SuspenseWrapper><ThreatMap /></SuspenseWrapper>} />
              <Route path="/book" element={<SuspenseWrapper><Book /></SuspenseWrapper>} />
              <Route path="/sitemap" element={<SuspenseWrapper><Sitemap /></SuspenseWrapper>} />


              {/* Landing Pages */}
              <Route path="/cybersecurity" element={<SuspenseWrapper><CybersecurityLanding /></SuspenseWrapper>} />
              <Route path="/managed-it" element={<SuspenseWrapper><ManagedITLanding /></SuspenseWrapper>} />
              <Route path="/continuity" element={<SuspenseWrapper><ContinuityLanding /></SuspenseWrapper>} />

              {/* Legal Pages */}
              <Route path="/privacy-policy" element={<SuspenseWrapper><PrivacyPolicy /></SuspenseWrapper>} />
              <Route path="/terms-of-service" element={<SuspenseWrapper><TermsOfService /></SuspenseWrapper>} />

              {/* Case Studies */}
              <Route path="/case-studies/law-firm" element={<SuspenseWrapper><LawFirmCaseStudy /></SuspenseWrapper>} />
              <Route path="/case-studies/financial-services" element={<SuspenseWrapper><FinancialServicesCaseStudy /></SuspenseWrapper>} />
              <Route path="/case-studies/consulting" element={<SuspenseWrapper><ConsultingCaseStudy /></SuspenseWrapper>} />
              <Route path="/case-studies/architecture" element={<SuspenseWrapper><ArchitectureCaseStudy /></SuspenseWrapper>} />
              <Route path="/case-studies/startup" element={<SuspenseWrapper><StartupCaseStudy /></SuspenseWrapper>} />

              {/* 404 Route - Must be last */}
              <Route path="*" element={<SuspenseWrapper><NotFound /></SuspenseWrapper>} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </ThemeProvider>
  </HelmetProvider>
  );
};

export default App;
