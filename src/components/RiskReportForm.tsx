
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { clarityUtils } from '@/components/Clarity';

const companySizes = [
  "5-25 employees",
  "26-50 employees",
  "51-100 employees",
  "101-250 employees",
  "251-500 employees",
  "500+ employees",
];

export default function RiskReportForm() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    companyName: '',
    companySize: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Track form field interactions
    clarityUtils.event("risk_form_field_filled");
    clarityUtils.setTag("field_name", name);
  };

  const handleSelectChange = (value: string) => {
    setFormData({ ...formData, companySize: value });

    // Track company size selection
    clarityUtils.event("risk_form_company_size_selected");
    clarityUtils.setTag("company_size", value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Track form submission attempt
    clarityUtils.event("risk_form_submitted");
    clarityUtils.setTag("form_type", "risk_report");
    clarityUtils.setTag("company_size", formData.companySize);

    // Simulate form submission
    setTimeout(() => {
      setIsLoading(false);

      // Track successful submission
      clarityUtils.event("risk_form_submission_success");
      clarityUtils.setTag("conversion_type", "risk_report_request");

      toast({
        title: "Success!",
        description: "Your risk report request has been submitted. Check your email shortly.",
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        companyName: '',
        companySize: '',
      });
    }, 1000);
    
    // In future implementation, this would connect to a webhook
    // Form submission logging removed for production
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Input 
          placeholder="Full Name" 
          name="name"
          value={formData.name}
          onChange={handleChange}
          required
        />
      </div>
      <div>
        <Input 
          type="email" 
          placeholder="Work Email" 
          name="email"
          value={formData.email}
          onChange={handleChange}
          required
        />
      </div>
      <div>
        <Input 
          placeholder="Company Name" 
          name="companyName"
          value={formData.companyName}
          onChange={handleChange}
          required
        />
      </div>
      <div>
        <Select 
          value={formData.companySize} 
          onValueChange={handleSelectChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Company Size" />
          </SelectTrigger>
          <SelectContent>
            {companySizes.map((size) => (
              <SelectItem key={size} value={size}>
                {size}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Button type="submit" className="w-full btn-primary" disabled={isLoading}>
        {isLoading ? "Processing..." : "Get Your Free Risk Report"}
      </Button>
    </form>
  );
}
