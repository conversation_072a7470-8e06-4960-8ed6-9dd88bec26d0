import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const umamiWebsiteId = 'c85d131d-9e6b-4a43-843a-e090173869b9';
const umamiScriptUrl = 'https://cloud.umami.is/script.js';

const Analytics = () => {
  const location = useLocation();

  useEffect(() => {
    const script = document.createElement('script');
    script.async = true;
    script.src = umamiScriptUrl;
    script.setAttribute('data-website-id', umamiWebsiteId);
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  useEffect(() => {
    if (window.umami) {
      window.umami.track(location.pathname + location.search);
    }
  }, [location]);

  return null;
};

export default Analytics;