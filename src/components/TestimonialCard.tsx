
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface TestimonialCardProps {
  quote: string;
  author: string;
  role: string;
  company: string;
  avatarUrl?: string;
  darkMode?: boolean;
}

export default function TestimonialCard({
  quote,
  author,
  role,
  company,
  avatarUrl,
  darkMode = false
}: TestimonialCardProps) {
  const initials = author
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();

  return (
    <Card className={`h-full transition-all duration-300 ${darkMode ? 'bg-gray-800 border-gray-700 hover:border-gray-600' : 'bg-white'}`}>
      <CardContent className="pt-6 px-5 pb-5 flex flex-col h-full">
        <div className="mb-4 flex-grow">
          <svg
            className={`h-8 w-8 mb-2 ${darkMode ? 'text-teal-400 opacity-80' : 'text-teclara-teal opacity-40'}`}
            fill="currentColor"
            viewBox="0 0 32 32"
            aria-hidden="true"
          >
            <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
          </svg>
          <p className={`${darkMode ? 'text-gray-300' : 'text-muted-foreground'}`}>{quote}</p>
        </div>
        <div className="flex items-center mt-4">
          <Avatar className="h-10 w-10">
            {avatarUrl ? <AvatarImage src={avatarUrl} alt={author} /> : null}
            <AvatarFallback className={`${darkMode ? 'bg-teal-600' : 'bg-teclara-navy'} text-white`}>
              {initials}
            </AvatarFallback>
          </Avatar>
          <div className="ml-3">
            <p className={`text-sm font-medium ${darkMode ? 'text-white' : ''}`}>{author}</p>
            <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-muted-foreground'}`}>
              {role}, {company}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
