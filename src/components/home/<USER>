import {
  Activity,
  Shield,
  FileText,
  User,
  Users,
  Cloud,
  RefreshCw,
  Headphones,
  TrendingUp,
  DollarSign,
  HardDrive,
  CheckCircle,
  Flag,
} from "lucide-react";

export const partnerLogos = [
  {
    name: "Huntress",
    image: "/uploads/partnerlogo/huntress_logo.png",
    width: 100,
  },
  {
    name: "SentinelOne",
    image: "/uploads/partnerlogo/sentinelone_logo.png",
    width: 120,
  },
  {
    name: "Action1",
    image: "/uploads/partnerlogo/action1_logo.png",
    width: 100,
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    image: "/uploads/partnerlogo/blumira_logo.png",
    width: 100,
  },
  { name: "<PERSON><PERSON>", image: "/uploads/partnerlogo/avanan_logo.png", width: 120 },
  {
    name: "Dropsuite",
    image: "/uploads/partnerlogo/dropsuite-logo-color.png",
    width: 100,
  },
  {
    name: "Usecure",
    image: "/uploads/partnerlogo/usecure_logo.png",
    width: 100,
  },
  {
    name: "Bitdefender",
    image: "/uploads/partnerlogo/bitdefender_logo.png",
    width: 100,
  },
  {
    name: "Harmony E-C",
    image: "/uploads/partnerlogo/harmony-e-c-logo.png",
    width: 100,
  },
];

export const services = [
  {
    title: "Cybersecurity",
    description: "24/7 Threat Protection & Response",
    icon: Shield,
    iconColor: "text-[#FF1717]",
    iconBg: "bg-[#FF1717]/10",
    borderColor: "border-[#FF1717]/20",
    hoverBorderColor: "hover:border-[#FF1717]/40",
    bgGradient: "from-[#FF1717]/5 to-transparent",
    glowColor: "hover:shadow-[0_0_20px_rgba(255,23,23,0.15)]",
    features: [
      "AI-Powered Threat Detection",
      "Rapid Incident Response",
      "Compliance Made Simple",
    ],
  },
  {
    title: "Managed IT",
    description: "Proactive Operations & Support",
    icon: RefreshCw,
    iconColor: "text-[#6B8EF5]",
    iconBg: "bg-[#6B8EF5]/10",
    borderColor: "border-[#6B8EF5]/20",
    hoverBorderColor: "hover:border-[#6B8EF5]/40",
    bgGradient: "from-[#6B8EF5]/5 to-transparent",
    glowColor: "hover:shadow-[0_0_20px_rgba(107,142,245,0.15)]",
    features: ["Expert Help Desk Support", "Strategic IT Planning", "Simple, Predictable Pricing"],
  },
  {
    title: "Cloud Solutions",
    description: "Secure & Scalable Infrastructure",
    icon: Cloud,
    iconColor: "text-[#10B981]",
    iconBg: "bg-[#10B981]/10",
    borderColor: "border-[#10B981]/20",
    hoverBorderColor: "hover:border-[#10B981]/40",
    bgGradient: "from-[#10B981]/5 to-transparent",
    glowColor: "hover:shadow-[0_0_20px_rgba(16,185,129,0.15)]",
    features: ["Microsoft 365 & Google Workspace", "Secure Backup with Rapid Recovery", "Seamless Cloud Migration"],
  },
  {
    title: "Business Growth",
    description: "Strategic Technology Guidance",
    icon: TrendingUp,
    iconColor: "text-[#F59E0B]",
    iconBg: "bg-[#F59E0B]/10",
    borderColor: "border-[#F59E0B]/20",
    hoverBorderColor: "hover:border-[#F59E0B]/40",
    bgGradient: "from-[#F59E0B]/5 to-transparent",
    glowColor: "hover:shadow-[0_0_20px_rgba(245,158,11,0.15)]",
    features: ["Virtual CIO Services", "Technology Roadmap Planning", "Scalable Growth Solutions"],
  },
];

export const trustIndicators = [
  {
    title: "Enterprise-Grade",
    description: "Big-business protection for small and midsize companies",
    icon: Shield,
  },
  {
    title: "Canadian Owned",
    description: "Local expertise with deep understanding of business needs",
    icon: Flag,
  },
  {
    title: "Proven Results",
    description: "Trusted by hundreds of businesses across Ontario",
    icon: TrendingUp,
  },
];

export const keyStats = [
  { number: "24/7", label: "Always On Security", icon: Shield },
  { number: "100%", label: "Canadian Owned", icon: Flag },
  { number: "SMB", label: "Focused Solutions", icon: TrendingUp },
  { number: "Expert", label: "Local Support", icon: Headphones },
];

export const industries = [
  "Law Firms",
  "Financial Services",
  "Healthcare",
  "Professional Services",
  "Manufacturing",
  "Non-Profits",
];
