import React from 'react';
import { Check } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import LeadGenerationButton from '@/components/LeadGenerationButton';

interface Feature {
  name: string;
  included: boolean;
}

interface PlanCardProps {
  name: string;
  price: string;
  description: string;
  features: Feature[];
  popular?: boolean;
  darkMode?: boolean;
  cta?: { label: string; href: string; onClick?: (e: React.MouseEvent) => void };
  serverAddonPrice?: React.ReactNode;
  onHover?: () => void;
  buttonClassName?: string;
  includeServerInfo?: boolean;
  serverInclusionText?: string;
}

export default function PlanCard({
  name,
  price,
  description,
  features,
  popular = false,
  darkMode = false,
  cta = { label: "Get Started", href: "/contact" },
  serverAddonPrice,
  onHover,
  buttonClassName = '',
  includeServerInfo = false,
  serverInclusionText = "✓ Includes 1 server per 10 users at no extra cost",
}: PlanCardProps) {
  return (
    <Card
      className={`relative overflow-hidden transition-all duration-300 hover:shadow-lg ${
        popular 
          ? 'border-teclara-teal scale-105 shadow-lg' 
          : darkMode 
            ? 'border-gray-700 hover:border-gray-600' 
            : 'border-border'
      } ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-card'}`}
      onMouseEnter={onHover}
      onFocus={onHover}
    >
      {includeServerInfo && (
        <div className={`px-4 py-3 text-center border-b relative ${
          darkMode ? 'bg-[#6B8EF5]/10 border-[#6B8EF5]/20 text-[#6B8EF5]' : 'bg-blue-50 border-blue-200 text-blue-700'
        } ${popular ? 'pl-28' : ''}`}>
          <div className="text-xs font-medium">
            {serverInclusionText}
          </div>
        </div>
      )}

      {popular && (
        <div className={`absolute ${includeServerInfo ? 'top-3' : 'top-0'} left-0 text-xs font-bold px-4 py-1.5 rounded-br-lg z-10 ${
          darkMode
            ? 'bg-gradient-to-r from-teal-400 to-blue-500 text-gray-900'
            : 'bg-teclara-teal text-teclara-navy'
        }`}>
          BEST VALUE
        </div>
      )}

      <CardHeader className={`pb-6 pt-8 px-6 text-center ${darkMode ? 'border-b border-gray-700' : ''}`}>
        <h3 className={`text-xl font-bold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>{name}</h3>
        <div className="flex flex-col items-center mb-2">
          <div className="flex justify-center items-end">
            <span className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>${price}</span>
            <span className={`text-sm ml-1 ${darkMode ? 'text-gray-400' : 'text-muted-foreground'}`}>/user/month</span>
          </div>
          {serverAddonPrice && (
            <div className={`text-xs mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>{serverAddonPrice}</div>
          )}
        </div>
        <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{description}</p>
      </CardHeader>
      
      <CardContent className="px-6 py-6">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li 
              key={index} 
              className="flex items-start text-sm"
            >
              <Check className={`w-5 h-5 mr-2 flex-shrink-0 ${
                feature.included 
                  ? darkMode 
                    ? 'text-teal-400' 
                    : 'text-teclara-teal' 
                  : darkMode 
                    ? 'text-gray-600' 
                    : 'text-muted-foreground'
              }`} />
              <span className={feature.included ? (darkMode ? 'text-gray-200' : '') : (darkMode ? 'text-gray-500 line-through' : 'text-muted-foreground line-through')}>
                {feature.name}
              </span>
            </li>
          ))}
        </ul>
      </CardContent>
      
      <CardFooter className="px-6 pb-8">
        <div className="flex justify-center w-full">
          <LeadGenerationButton
            formType="SIGNUP"
            buttonText={cta?.label || 'Get Started'}
            location={`Pricing Plan Card: ${name}`}
            utmParameters={{ package: name.toLowerCase() }}
            usePopup={true}
            className={`w-full max-w-xs transition-all inline-block text-center no-underline font-semibold text-sm rounded-lg px-3 py-3 mt-4 ${buttonClassName}`}
          />
        </div>
      </CardFooter>
    </Card>
  );
}
