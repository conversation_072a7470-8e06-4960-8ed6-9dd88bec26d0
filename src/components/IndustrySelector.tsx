import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { clarityUtils } from '@/components/Clarity';

interface IndustrySelectorProps {
  currentIndustry: string;
}

const IndustrySelector = ({ currentIndustry }: IndustrySelectorProps) => {
  const location = useLocation();

  return (
    <div className="flex justify-center items-center py-8">
      <Tabs value={currentIndustry} className="w-full max-w-4xl flex justify-center">
        <TabsList className="bg-[#F9FAFB] shadow-lg p-2 rounded-full inline-flex">
          <Link to="/case-studies/law-firm">
            <TabsTrigger
              value="law"
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
              onClick={() => {
                clarityUtils.event("industry_selector_clicked");
                clarityUtils.setTag("industry_selected", "law_firm");
                clarityUtils.setTag("selector_location", "case_studies");
              }}
            >
              Law Firms
            </TabsTrigger>
          </Link>
          <Link to="/case-studies/financial-services">
            <TabsTrigger
              value="finance"
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
              onClick={() => {
                clarityUtils.event("industry_selector_clicked");
                clarityUtils.setTag("industry_selected", "financial_services");
                clarityUtils.setTag("selector_location", "case_studies");
              }}
            >
              Financial Services
            </TabsTrigger>
          </Link>
          <Link to="/case-studies/consulting">
            <TabsTrigger
              value="consulting"
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
              onClick={() => {
                clarityUtils.event("industry_selector_clicked");
                clarityUtils.setTag("industry_selected", "consulting");
                clarityUtils.setTag("selector_location", "case_studies");
              }}
            >
              Consulting
            </TabsTrigger>
          </Link>
          <Link to="/case-studies/architecture">
            <TabsTrigger
              value="architecture"
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
              onClick={() => {
                clarityUtils.event("industry_selector_clicked");
                clarityUtils.setTag("industry_selected", "architecture");
                clarityUtils.setTag("selector_location", "case_studies");
              }}
            >
              Architecture
            </TabsTrigger>
          </Link>
          <Link to="/case-studies/startup">
            <TabsTrigger
              value="startup"
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
              onClick={() => {
                clarityUtils.event("industry_selector_clicked");
                clarityUtils.setTag("industry_selected", "startup");
                clarityUtils.setTag("selector_location", "case_studies");
              }}
            >
              Startups
            </TabsTrigger>
          </Link>
        </TabsList>
      </Tabs>
    </div>
  );
};

export default IndustrySelector; 