import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

interface IndustrySelectorProps {
  currentIndustry: string;
}

const IndustrySelector = ({ currentIndustry }: IndustrySelectorProps) => {
  const location = useLocation();

  return (
    <div className="flex justify-center items-center py-8">
      <Tabs value={currentIndustry} className="w-full max-w-4xl flex justify-center">
        <TabsList className="bg-[#F9FAFB] shadow-lg p-2 rounded-full inline-flex">
          <Link to="/case-studies/law-firm">
            <TabsTrigger 
              value="law" 
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
            >
              Law Firms
            </TabsTrigger>
          </Link>
          <Link to="/case-studies/financial-services">
            <TabsTrigger 
              value="finance" 
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
            >
              Financial Services
            </TabsTrigger>
          </Link>
          <Link to="/case-studies/consulting">
            <TabsTrigger 
              value="consulting" 
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
            >
              Consulting
            </TabsTrigger>
          </Link>
          <Link to="/case-studies/architecture">
            <TabsTrigger 
              value="architecture" 
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
            >
              Architecture
            </TabsTrigger>
          </Link>
          <Link to="/case-studies/startup">
            <TabsTrigger 
              value="startup" 
              className="rounded-full px-6 py-3 data-[state=active]:bg-[#FF1919] data-[state=active]:text-white transition-all"
            >
              Startups
            </TabsTrigger>
          </Link>
        </TabsList>
      </Tabs>
    </div>
  );
};

export default IndustrySelector; 