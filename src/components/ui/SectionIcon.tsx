import React from "react";
import { LucideIcon } from "lucide-react";

interface SectionIconProps {
  icon?: LucideIcon;
  className?: string;
}

export function SectionIcon({ 
  icon: Icon, 
  className = "w-10 h-10 text-teclara-primary mx-auto mb-4" 
}: SectionIconProps) {
  if (Icon) {
    return <Icon className={className} />;
  }
  
  return (
    <svg
      className={className}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="12" y="22" width="76" height="10" rx="2" fill="currentColor" />
      <rect x="12" y="45" width="76" height="10" rx="2" fill="currentColor" />
      <rect x="12" y="68" width="76" height="10" rx="2" fill="currentColor" />
    </svg>
  );
}