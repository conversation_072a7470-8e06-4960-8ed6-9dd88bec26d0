import { Helmet } from 'react-helmet-async';

const GlobalMetaTags = () => {
  return (
    <Helmet>
      {/* Default meta tags */}
      <meta name="robots" content="index, follow" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#1A1A1A" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Default Open Graph tags */}
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="Teclara Technologies" />
      <meta property="og:locale" content="en_US" />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image" content="https://teclara.tech/uploads/teclara_logo_black_text.png" />
      <meta property="og:image:alt" content="Teclara - Enterprise Cybersecurity & IT Solutions" />
      
      {/* Default Twitter tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@teclaratech" />
      <meta name="twitter:creator" content="@teclaratech" />
      <meta name="twitter:image" content="https://teclara.tech/uploads/teclara_logo_black_text.png" />
      <meta name="twitter:image:alt" content="Teclara - Enterprise Cybersecurity & IT Solutions" />
      
      {/* Default keywords */}
      <meta name="keywords" content="cybersecurity, managed IT, business continuity, data protection, compliance, network security, cloud security, endpoint protection, Oakville, Hamilton, Milton, St. Catharines, Halton Region" />
      
      {/* Author */}
      <meta name="author" content="Teclara Technologies" />
    </Helmet>
  );
};

export default GlobalMetaTags;