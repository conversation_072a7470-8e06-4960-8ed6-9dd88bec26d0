import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Shield, Cloud, Mail, Building2, FileText, Scale, Landmark, Rocket, LifeBuoy } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const buildNumber = import.meta.env.VITE_BUILD_NUMBER 
    ? import.meta.env.VITE_BUILD_NUMBER.substring(0, 7)
    : 'local';
  
  return (
    <footer className="bg-gray-900 text-white py-8 md:py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="col-span-1">
            <Link to="/" className="inline-block">
              <img
                src="/uploads/teclara_logo_white_text.png"
                alt="Teclara Technologies Logo"
                className="h-10 mb-4"
              />
            </Link>
            <p className="text-sm text-gray-300 mb-4">
              Enterprise-grade cybersecurity and IT support, simplified for Canadian SMBs.
            </p>
            <div className="flex flex-col space-y-4">
              <div className="flex items-center space-x-4 text-gray-300">
                <a href="https://linkedin.com/company/teclara" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                  <div className="rounded-full border border-gray-600 p-2.5 hover:border-teclara-primary hover:text-teclara-primary transition-colors">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <title>LinkedIn</title>
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.454C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.225 0z" />
                    </svg>
                  </div>
                </a>
                <a href="https://www.facebook.com/profile.php?id=61574817958573" aria-label="Facebook" target="_blank" rel="noopener noreferrer">
                  <div className="rounded-full border border-gray-600 p-2.5 hover:border-teclara-primary hover:text-teclara-primary transition-colors">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <title>Facebook</title>
                      <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                    </svg>
                  </div>
                </a>
              </div>
            </div>
          </div>

          {/* Solutions & Services */}
          <div className="col-span-1">
            <h4 className="text-lg font-semibold mb-4 text-white">Solutions & Services</h4>
            <ul className="space-y-2">
              <li><Link to="/solutions" className="text-gray-300 hover:text-teclara-primary transition-colors">Our Solutions</Link></li>
              <li><Link to="/pricing" className="text-gray-300 hover:text-teclara-primary transition-colors">Pricing & Plans</Link></li>
              <li><Link to="/threatmap" className="text-gray-300 hover:text-teclara-primary transition-colors">Threat Map</Link></li>
              <li><Link to="/industries" className="text-gray-300 hover:text-teclara-primary transition-colors">Industries We Serve</Link></li>
            </ul>
          </div>

          {/* Case Studies */}
          <div className="col-span-1">
            <h4 className="text-lg font-semibold mb-4 text-white">Case Studies</h4>
            <ul className="space-y-2">
              <li><Link to="/case-studies/law-firm" className="text-gray-300 hover:text-teclara-primary transition-colors">Law Firm</Link></li>
              <li><Link to="/case-studies/financial-services" className="text-gray-300 hover:text-teclara-primary transition-colors">Financial Services</Link></li>
              <li><Link to="/case-studies/consulting" className="text-gray-300 hover:text-teclara-primary transition-colors">Consulting</Link></li>
              <li><Link to="/case-studies/architecture" className="text-gray-300 hover:text-teclara-primary transition-colors">Architecture</Link></li>
              <li><Link to="/case-studies/startup" className="text-gray-300 hover:text-teclara-primary transition-colors">Startup</Link></li>
            </ul>
          </div>

          {/* Company & Resources */}
          <div className="col-span-1">
            <h4 className="text-lg font-semibold mb-4 text-white">Company & Resources</h4>
            <ul className="space-y-2">
              <li><Link to="/about" className="text-gray-300 hover:text-teclara-primary transition-colors">About Us</Link></li>
              <li><Link to="/contact" className="text-gray-300 hover:text-teclara-primary transition-colors">Contact Us</Link></li>
              <li><a href="https://status.teclara.tech" className="text-gray-300 hover:text-teclara-primary transition-colors" target="_blank" rel="noopener noreferrer">Status Page</a></li>
            </ul>
          </div>

          {/* Contact */}
          <div className="col-span-1">
            <h4 className="text-lg font-semibold mb-4 text-white">Contact</h4>
            <ul className="space-y-3">
              <li className="flex items-start">
                <Mail className="w-5 h-5 text-teclara-primary mr-2 mt-0.5" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-teclara-primary transition-colors"><EMAIL></a>
              </li>
              <li className="flex items-start">
                <LifeBuoy className="w-5 h-5 text-teclara-primary mr-2 mt-0.5" />
                <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-teclara-primary transition-colors"><EMAIL></a>
              </li>
              <li className="flex items-start">
                <Shield className="w-5 h-5 text-teclara-primary mr-2 mt-0.5" />
                <a href="https://portal.teclara.tech/" target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-teclara-primary transition-colors">Portal</a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom */}
        <div className="mt-6 pt-6">
          <div className="w-1/4 mx-auto border-t border-gray-700"></div>
          {/* Chamber of Commerce Logos */}
          <div className="flex flex-col space-y-3 mt-6">
            <h5 className="text-sm font-medium text-gray-300 text-center">Proud Member of:</h5>
            <div className="flex flex-wrap items-center justify-center gap-16">
              <img
                src="/uploads/cc/OCC_White_Stacked.png"
                alt="Oakville Chamber of Commerce"
                className="h-16 object-contain"
              />
              <img
                src="/uploads/cc/MCClogo.png"
                alt="Mississauga Chamber of Commerce"
                className="h-16 object-contain"
              />
              <img
                src="/uploads/cc/Hamilton-Chamber-Member-Logo.png"
                alt="Hamilton Chamber of Commerce"
                className="h-16 object-contain"
              />
            </div>
          </div>
          <div className="border-t border-gray-700 mt-6 pt-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4 text-sm text-gray-400 mb-4 md:mb-0">
                <span>© {currentYear} Teclara Technologies Inc. All rights reserved. Build: <span className="italic">{buildNumber}</span></span>
              </div>
              <div className="flex space-x-4 text-sm text-gray-400">
                <Link to="/privacy-policy" className="hover:text-teclara-primary transition-colors">Privacy Policy</Link>
                <Link to="/terms-of-service" className="hover:text-teclara-primary transition-colors">Terms of Service</Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
