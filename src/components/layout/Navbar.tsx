import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ButtonLink } from '@/components/ui/button-link';
import { Menu, X, ExternalLink } from 'lucide-react';
import { FORMS } from '@/utils/formConstants';
import LeadGenerationButton from '@/components/LeadGenerationButton';
import { clarityUtils } from '@/components/Clarity';

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    clarityUtils.event("mobile_menu_toggled");
    clarityUtils.setTag("menu_action", isMenuOpen ? "closed" : "opened");
  };

  const navigation = [
    { name: "Home", href: "/" },
    { name: "Solutions", href: "/solutions" },
    { name: "Industries", href: "/industries" },
    { name: "Pricing", href: "/pricing" },
    { name: "About", href: "/about" },
    { name: "Contact", href: "/contact" },
    { name: "Threat Map", href: "/threatmap" },
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-gray-900/70 backdrop-blur-md border-b border-white/10 text-white transition-all duration-300">
      <div className="container flex items-center justify-between h-16 md:h-20">
        {/* Logo - Optimized for all screen sizes */}
        <Link
          to="/"
          className="flex items-center py-2"
          onClick={() => {
            clarityUtils.event("logo_clicked");
            clarityUtils.setTag("navigation_source", "header_logo");
          }}
        >
          <img 
            src="/uploads/teclara_logo_white_text.png" 
            alt="Teclara Technologies Logo" 
            className="h-7 md:h-8 w-auto object-contain" 
            style={{ maxWidth: '180px' }}
          />
        </Link>

        {/* Desktop Navigation - Centered */}
        <nav className="hidden md:flex items-center justify-center flex-1">
          <div className="flex items-center space-x-1 lg:space-x-2">
            {navigation.map((link) => (
              <Link
                key={link.name}
                to={link.href}
                className={`px-3 lg:px-4 py-2 text-sm lg:text-base font-medium transition-colors rounded-md text-center min-w-[80px] ${
                  location.pathname === link.href
                    ? 'bg-white/10 text-teclara-primary'
                    : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
                onClick={() => {
                  clarityUtils.event("navigation_clicked");
                  clarityUtils.setTag("nav_destination", link.name.toLowerCase());
                  clarityUtils.setTag("nav_source", "desktop_menu");
                }}
              >
                {link.name}
              </Link>
            ))}
          </div>
        </nav>

        {/* Mobile Navigation Toggle - Improved tap target */}
        <div className="md:hidden">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={toggleMenu} 
            className="text-white p-2 -mr-2"
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </Button>
        </div>

        {/* CTA Buttons - Desktop Only with improved visibility */}
        <div className="hidden md:flex items-center space-x-3">
          <ButtonLink
            href="https://teclara.rmmservice.ca/"
            target="_blank"
            className="px-3 lg:px-4 py-2 text-sm lg:text-base font-medium transition-colors rounded-md text-center min-w-[80px] bg-transparent border border-white/20 text-white/80 hover:bg-teclara-primary hover:text-white hover:border-teclara-primary flex items-center gap-2"
          >
            Portal
            <ExternalLink className="h-4 w-4" />
          </ButtonLink>
        </div>
      </div>

      {/* Mobile Menu - Improved for touch and readability */}
      {isMenuOpen && (
        <div className="md:hidden bg-gray-900/70 backdrop-blur-md border-t border-white/10 shadow-lg transition-all duration-300">
          <div className="container py-4 space-y-1">
            <div className="flex flex-col items-center">
              {navigation.map((link) => (
                <Link
                  key={link.name}
                  to={link.href}
                  className={`w-full text-center px-4 py-3.5 text-base font-medium transition-colors rounded-md ${
                    location.pathname === link.href
                      ? 'bg-white/10 text-teclara-primary'
                      : 'text-white/80 hover:bg-white/10 hover:text-white'
                  }`}
                  onClick={() => {
                    setIsMenuOpen(false);
                    clarityUtils.event("navigation_clicked");
                    clarityUtils.setTag("nav_destination", link.name.toLowerCase());
                    clarityUtils.setTag("nav_source", "mobile_menu");
                  }}
                >
                  {link.name}
                </Link>
              ))}
            </div>
            <div className="pt-3 px-4 space-y-3 flex flex-col items-center">
              <ButtonLink
                href="https://portal.teclara.tech/"
                target="_blank"
                className="w-full text-center px-4 py-3.5 text-base font-medium transition-colors rounded-md bg-transparent border border-white/20 text-white/80 hover:bg-teclara-primary hover:text-white hover:border-teclara-primary flex items-center justify-center gap-2"
              >
                Portal
                <ExternalLink className="h-4 w-4" />
              </ButtonLink>
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
