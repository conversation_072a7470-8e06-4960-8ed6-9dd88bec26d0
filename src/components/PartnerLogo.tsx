import React from "react";
import { clarityUtils } from "@/components/Clarity";

interface PartnerLogoProps {
  name: string;
  width?: number;
  height?: number;
  image?: string;
}

export default function PartnerLogo({
  name,
  width = 200,
  height = 100,
  image,
}: PartnerLogoProps) {
  // Special handling for logos that need cropping
  const needsCropping = name === "SentinelOne" || name === "Action1";

  return (
    <div
      style={{ width, height }}
      className="cursor-pointer"
      onClick={() => {
        clarityUtils.event("partner_logo_clicked");
        clarityUtils.setTag("partner_name", name.toLowerCase().replace(/\s+/g, '_'));
        clarityUtils.setTag("interaction_type", "logo_click");
      }}
    >
      {image ? (
        <img
          src={image}
          alt={name}
          style={{
            width: "100%",
            height: "100%",
            objectFit: needsCropping ? "cover" : "contain",
            objectPosition: needsCropping ? "center center" : "center",
          }}
        />
      ) : (
        <span>{name}</span>
      )}
    </div>
  );
}
