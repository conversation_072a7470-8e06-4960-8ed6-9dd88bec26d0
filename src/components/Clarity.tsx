import { useEffect } from 'react';
import Clarity from '@microsoft/clarity';

interface ClarityProps {
  projectId: string;
  enabled?: boolean;
}

const ClarityComponent: React.FC<ClarityProps> = ({ 
  projectId, 
  enabled = true 
}) => {
  useEffect(() => {
    if (!enabled || !projectId) {
      return;
    }

    // Initialize Microsoft Clarity
    try {
      Clarity.init(projectId);
      console.log('Microsoft Clarity initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Microsoft Clarity:', error);
    }

    // Cleanup function
    return () => {
      // Clarity doesn't provide a cleanup method, but we can log the unmount
      console.log('Clarity component unmounted');
    };
  }, [projectId, enabled]);

  // This component doesn't render anything
  return null;
};

export default ClarityComponent;

// Export utility functions for tracking
export const clarityUtils = {
  // Set custom tags
  setTag: (key: string, value: string | string[]) => {
    try {
      Clarity.setTag(key, value);
    } catch (error) {
      console.error('Failed to set Clarity tag:', error);
    }
  },

  // Track custom events
  event: (eventName: string) => {
    try {
      Clarity.event(eventName);
    } catch (error) {
      console.error('Failed to track Clarity event:', error);
    }
  },

  // Identify users
  identify: (
    customId: string, 
    customSessionId?: string, 
    customPageId?: string, 
    friendlyName?: string
  ) => {
    try {
      Clarity.identify(customId, customSessionId, customPageId, friendlyName);
    } catch (error) {
      console.error('Failed to identify user in Clarity:', error);
    }
  },

  // Set cookie consent
  consent: (hasConsent: boolean = true) => {
    try {
      Clarity.consent(hasConsent);
    } catch (error) {
      console.error('Failed to set Clarity consent:', error);
    }
  },

  // Upgrade session priority
  upgrade: (reason: string) => {
    try {
      Clarity.upgrade(reason);
    } catch (error) {
      console.error('Failed to upgrade Clarity session:', error);
    }
  }
};
