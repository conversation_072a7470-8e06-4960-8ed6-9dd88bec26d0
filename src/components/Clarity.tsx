import { useEffect } from 'react';
import Clarity from '@microsoft/clarity';

interface ClarityProps {
  projectId: string;
  enabled?: boolean;
}

const ClarityComponent: React.FC<ClarityProps> = ({ 
  projectId, 
  enabled = true 
}) => {
  useEffect(() => {
    if (!enabled || !projectId) {
      return;
    }

    // Initialize Microsoft Clarity
    try {
      Clarity.init(projectId);
      console.log('Microsoft Clarity initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Microsoft Clarity:', error);
    }

    // Global error tracking
    const handleError = (event: ErrorEvent) => {
      try {
        Clarity.event('javascript_error');
        Clarity.setTag('error_message', event.message);
        Clarity.setTag('error_filename', event.filename || 'unknown');
        Clarity.setTag('error_line', event.lineno?.toString() || 'unknown');
      } catch (clarityError) {
        console.error('Failed to track error in Clarity:', clarityError);
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      try {
        Clarity.event('promise_rejection');
        Clarity.setTag('rejection_reason', event.reason?.toString() || 'unknown');
      } catch (clarityError) {
        console.error('Failed to track promise rejection in Clarity:', clarityError);
      }
    };

    // Add error listeners
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // Cleanup function
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      console.log('Clarity component unmounted');
    };
  }, [projectId, enabled]);

  // This component doesn't render anything
  return null;
};

export default ClarityComponent;

// Export utility functions for tracking
export const clarityUtils = {
  // Set custom tags
  setTag: (key: string, value: string | string[]) => {
    try {
      Clarity.setTag(key, value);
    } catch (error) {
      console.error('Failed to set Clarity tag:', error);
    }
  },

  // Track custom events
  event: (eventName: string) => {
    try {
      Clarity.event(eventName);
    } catch (error) {
      console.error('Failed to track Clarity event:', error);
    }
  },

  // Identify users
  identify: (
    customId: string, 
    customSessionId?: string, 
    customPageId?: string, 
    friendlyName?: string
  ) => {
    try {
      Clarity.identify(customId, customSessionId, customPageId, friendlyName);
    } catch (error) {
      console.error('Failed to identify user in Clarity:', error);
    }
  },

  // Set cookie consent
  consent: (hasConsent: boolean = true) => {
    try {
      Clarity.consent(hasConsent);
    } catch (error) {
      console.error('Failed to set Clarity consent:', error);
    }
  },

  // Upgrade session priority
  upgrade: (reason: string) => {
    try {
      Clarity.upgrade(reason);
    } catch (error) {
      console.error('Failed to upgrade Clarity session:', error);
    }
  },

  // Performance tracking utilities
  trackPerformance: () => {
    try {
      if ('performance' in window && 'getEntriesByType' in performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = Math.round(navigation.loadEventEnd - navigation.fetchStart);
          const domContentLoaded = Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart);

          Clarity.event('page_performance');
          Clarity.setTag('load_time_ms', loadTime.toString());
          Clarity.setTag('dom_content_loaded_ms', domContentLoaded.toString());

          // Track slow loading pages
          if (loadTime > 3000) {
            Clarity.event('slow_page_load');
            Clarity.setTag('slow_load_time_ms', loadTime.toString());
          }
        }
      }
    } catch (error) {
      console.error('Failed to track performance in Clarity:', error);
    }
  },

  // Track form validation errors
  trackFormError: (formType: string, fieldName: string, errorMessage: string) => {
    try {
      Clarity.event('form_validation_error');
      Clarity.setTag('form_type', formType);
      Clarity.setTag('field_name', fieldName);
      Clarity.setTag('error_message', errorMessage);
    } catch (error) {
      console.error('Failed to track form error in Clarity:', error);
    }
  },

  // Track loading states
  trackLoadingIssue: (componentName: string, issueType: string) => {
    try {
      Clarity.event('loading_issue');
      Clarity.setTag('component_name', componentName);
      Clarity.setTag('issue_type', issueType);
    } catch (error) {
      console.error('Failed to track loading issue in Clarity:', error);
    }
  }
};
