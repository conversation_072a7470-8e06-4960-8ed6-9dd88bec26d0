interface Organization {
  "@context": "https://schema.org";
  "@type": "Organization";
  name: string;
  url: string;
  logo: string;
  sameAs: string[];
}

interface Service {
  "@context": "https://schema.org";
  "@type": "Service";
  name: string;
  provider: {
    "@type": "Organization";
    name: string;
    url: string;
  };
  description: string;
  areaServed: string;
  serviceType: string;
}

interface CaseStudy {
  "@context": "https://schema.org";
  "@type": "Article";
  headline: string;
  description: string;
  image: string;
  datePublished: string;
  dateModified: string;
  author: {
    "@type": "Organization";
    name: string;
    url: string;
  };
  publisher: {
    "@type": "Organization";
    name: string;
    url: string;
    logo: {
      "@type": "ImageObject";
      url: string;
    };
  };
  mainEntityOfPage: {
    "@type": "WebPage";
    "@id": string;
  };
}

interface FAQPage {
  "@context": "https://schema.org";
  "@type": "FAQPage";
  mainEntity: {
    "@type": "Question";
    name: string;
    acceptedAnswer: {
      "@type": "Answer";
      text: string;
    };
  }[];
}

interface FAQ {
  question: string;
  answer: string;
}

export const baseOrganizationSchema: Organization = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Teclara Technologies",
  "url": "https://teclara.tech",
  "logo": "https://teclara.tech/uploads/teclara_logo_black_text.png",
  "sameAs": [
    "https://twitter.com/teclaratech",
    "https://www.linkedin.com/company/teclara",
    "https://github.com/teclara"
  ]
};

export const createServiceSchema = (
  name: string,
  description: string,
  serviceType: string,
  additionalInfo?: {
    areaServed?: string[];
    serviceOutput?: string[];
    hasOfferCatalog?: boolean;
    provider?: {
      name: string;
      url: string;
    };
  }
): Service => ({
  "@context": "https://schema.org",
  "@type": "Service",
  name,
  provider: additionalInfo?.provider || {
    "@type": "Organization",
    name: "Teclara Technologies",
    url: "https://teclara.tech"
  },
  description,
  areaServed: additionalInfo?.areaServed || [
    "Oakville",
    "Hamilton",
    "Milton",
    "St. Catharines",
    "Halton Region"
  ],
  serviceType,
  serviceOutput: additionalInfo?.serviceOutput || [
    "24/7 Monitoring",
    "Compliance Support",
    "Expert Support",
    "Proactive Maintenance"
  ],
  hasOfferCatalog: additionalInfo?.hasOfferCatalog || true,
  availableChannel: {
    "@type": "ServiceChannel",
    serviceType: "Online",
    availableLanguage: "English"
  }
});

export const createCaseStudySchema = (caseStudy: {
  title: string;
  description: string;
  datePublished: string;
  industry: string;
  clientName: string;
  challenges: string[];
  solutions: string[];
  results: string[];
  imageUrl: string;
}) => ({
  "@context": "https://schema.org",
  "@type": "CaseStudy",
  headline: caseStudy.title,
  description: caseStudy.description,
  datePublished: caseStudy.datePublished,
  author: {
    "@type": "Organization",
    name: "Teclara Technologies"
  },
  publisher: {
    "@type": "Organization",
    name: "Teclara Technologies",
    logo: {
      "@type": "ImageObject",
      url: "https://teclara.tech/uploads/teclara_logo_black_text.png"
    }
  },
  image: caseStudy.imageUrl,
  about: {
    "@type": "Thing",
    name: caseStudy.industry
  },
  client: {
    "@type": "Organization",
    name: caseStudy.clientName
  },
  mainEntity: {
    "@type": "Article",
    headline: caseStudy.title,
    description: caseStudy.description,
    articleBody: `
      Challenges: ${caseStudy.challenges.join(' ')}
      Solutions: ${caseStudy.solutions.join(' ')}
      Results: ${caseStudy.results.join(' ')}
    `
  }
});

export const createFAQSchema = (faqs: FAQ[]): FAQPage => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  mainEntity: faqs.map(faq => ({
    "@type": "Question",
    name: faq.question,
    acceptedAnswer: {
      "@type": "Answer",
      text: faq.answer
    }
  }))
});

export const createBreadcrumbSchema = (items: { name: string; url: string }[]) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  itemListElement: items.map((item, index) => ({
    "@type": "ListItem",
    position: index + 1,
    name: item.name,
    item: item.url
  }))
});

export const createOrganizationLocationSchema = () => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  ...baseOrganizationSchema,
  "address": {
    "@type": "PostalAddress",
    "addressLocality": "Oakville",
    "addressRegion": "ON",
    "addressCountry": "CA",
    "postalCode": "L6H 0C3"
  },
  "areaServed": [
    {
      "@type": "City",
      "name": "Oakville"
    },
    {
      "@type": "City",
      "name": "Hamilton"
    },
    {
      "@type": "City",
      "name": "Milton"
    },
    {
      "@type": "City",
      "name": "St. Catharines"
    }
  ],
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": "43.4675",
    "longitude": "-79.6877"
  }
});

export const createLocalBusinessSchema = (businessInfo: {
  name: string;
  description: string;
  address: {
    street: string;
    city: string;
    region: string;
    postalCode: string;
    country: string;
  };
  geo: {
    latitude: number;
    longitude: number;
  };
  phone: string;
  email: string;
  priceRange: string;
  openingHours: string[];
}) => ({
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "@id": "https://teclara.tech/#organization",
  name: businessInfo.name,
  description: businessInfo.description,
  image: "https://teclara.tech/uploads/teclara_logo_black_text.png",
  url: "https://teclara.tech",
  telephone: businessInfo.phone,
  email: businessInfo.email,
  priceRange: businessInfo.priceRange,
  address: {
    "@type": "PostalAddress",
    streetAddress: businessInfo.address.street,
    addressLocality: businessInfo.address.city,
    addressRegion: businessInfo.address.region,
    postalCode: businessInfo.address.postalCode,
    addressCountry: businessInfo.address.country
  },
  geo: {
    "@type": "GeoCoordinates",
    latitude: businessInfo.geo.latitude,
    longitude: businessInfo.geo.longitude
  },
  openingHoursSpecification: businessInfo.openingHours.map(hours => ({
    "@type": "OpeningHoursSpecification",
    dayOfWeek: hours.split(' ')[0],
    opens: hours.split(' ')[1].split('-')[0],
    closes: hours.split(' ')[1].split('-')[1]
  })),
  sameAs: [
    "https://twitter.com/teclaratech",
    "https://www.linkedin.com/company/teclara",
    "https://github.com/teclara",
    "https://www.facebook.com/profile.php?id=61574817958573"
  ]
});

export const createReviewSchema = (reviews: {
  author: string;
  reviewBody: string;
  reviewRating: number;
  datePublished: string;
}[]) => ({
  "@context": "https://schema.org",
  "@type": "AggregateRating",
  ratingValue: reviews.reduce((acc, review) => acc + review.reviewRating, 0) / reviews.length,
  reviewCount: reviews.length,
  bestRating: 5,
  worstRating: 1,
  "@graph": reviews.map(review => ({
    "@type": "Review",
    author: {
      "@type": "Person",
      name: review.author
    },
    reviewBody: review.reviewBody,
    reviewRating: {
      "@type": "Rating",
      ratingValue: review.reviewRating,
      bestRating: 5,
      worstRating: 1
    },
    datePublished: review.datePublished
  }))
});
