export const FORMS = {
  IT_CONSULT: {
    id: "jn2NjNdGzBus",
    defaultText: "Book a Free IT Consult",
    icon: "calendar",
    description: "Schedule a consultation for IT support, modernization, or digital transformation",
    leadType: "it_consult"
  },
  SECURITY_REVIEW: {
    id: "jn2NjNdGzBus",
    defaultText: "Get Your Free Security Review",
    icon: "calendar",
    description: "Get a comprehensive security assessment and risk analysis",
    leadType: "security_review"
  },
  GENERAL_CONTACT: {
    id: "jn2NjNdGzBus",
    defaultText: "Get in Touch",
    icon: "calendar",
    description: "General inquiries and contact form",
    leadType: "general_contact"
  },
  // Legacy mappings for backward compatibility
  SCHEDULE_CONSULTATION: {
    id: "jn2NjNdGzBus",
    defaultText: "Book a Free IT Consult",
    icon: "calendar",
    description: "Schedule a consultation for IT support, modernization, or digital transformation",
    leadType: "it_consult"
  },
  LEAD_GENERATION: {
    id: "jn2NjNdGzBus",
    defaultText: "Get Your Free Security Review",
    icon: "calendar",
    description: "Get a comprehensive security assessment and risk analysis",
    leadType: "security_review"
  },
  SIGNUP: {
    id: "fdV3BZR6uvus",
    defaultText: "Get Started",
    icon: "calendar",
    description: "Sign up for a plan",
    leadType: "signup"
  }
} as const;

// Type for form keys
export type FormType = keyof typeof FORMS;

// Helper function to get form configuration
export const getFormConfig = (type: FormType) => FORMS[type];