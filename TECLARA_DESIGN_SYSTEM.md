# Teclara Corp Design System - Base Theme Documentation

## Overview
This document defines the locked-down base theme for Teclara Corp website. All future development must adhere to these design standards to maintain consistency and brand integrity.

## Color Palette

### Primary Colors
- **Deep Navy Base**: `#060d25` - Primary background color
- **Secondary Navy**: `#04081a` - Secondary background, used in gradients
- **Royal Blue Accent**: `#6B8EF5` - Primary brand accent color for highlights, buttons, icons (lighter blue)
- **Royal Blue Hover**: `#5B7FE8` - Hover state for Royal Blue elements
- **Brand Red**: `#FF1717` - Strategic accent for alerts, warnings, and critical elements
- **Yellow Highlight**: `#FEC400` - Secondary accent for subtitles and special highlights
- **Pure White**: `#ffffff` - Text and contrast elements

### Legacy/Alternative Colors (Still in Use)
- **Teclara Primary Red**: `#D14019` - Original brand red from logo (still used in some components)
- **Crimson Red**: `#CC1414` - Hover variant for primary red
- **Turquoise**: `#34c5b6` - CSS variable brand primary (legacy system)
- **Dark Backgrounds**: `#1F252F`, `#2A2A2A`, `#181C23`, `#0A0A0A` - Various dark background shades
- **Gray Variants**: `#9ca3af`, `#6b7280`, `#374151`, `#4b5563` - Text and border grays

### Brand Red Usage Hierarchy
- **Full Opacity (100%)**: Critical alerts, emergency notifications, logo elements
- **Medium Opacity (40-60%)**: Warning states, important notices, error messages
- **Low Opacity (20-30%)**: Subtle backgrounds, section dividers, hover accents
- **Minimal Opacity (10-15%)**: Ghost backgrounds, very subtle highlights

### Background Gradients
```css
/* Standard page background */
bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]

/* Section backgrounds */
bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]
```

### Text Colors
- **Primary Text**: `text-white` - Main headings and important text
- **Secondary Text**: `text-gray-300` - Body text, descriptions
- **Accent Text**: `text-[#6B8EF5]` - Sparingly used for section titles only
- **Subtitle Text**: `text-[#FEC400]` - Service subtitles and badges only
- **Brand Red Text**: `text-[#FF1717]` - Threat emphasis, warnings, critical elements
- **Legacy Red Text**: `text-[#D14019]` - Original brand red (still used in some components)

### Professional Text Guidelines
- **Body Content**: Always use `text-gray-300` for professional appearance
- **Avoid**: Colorful text highlights within paragraphs
- **Use Color For**: Icons, section bars, badges, and titles only
- **Maintain**: Consistent text color throughout content sections

## Typography

### Headings
```css
/* Page Titles (H1) */
text-4xl md:text-6xl font-bold tracking-wide

/* Section Titles (H2) */
text-3xl md:text-4xl font-bold tracking-wide

/* Subsection Titles (H3) */
text-2xl md:text-3xl font-bold tracking-wide

/* Card Titles */
text-2xl font-bold tracking-wide

/* Small Titles */
text-xl font-bold tracking-wide
```

### Body Text
```css
/* Large Body Text */
text-xl md:text-2xl text-gray-300 leading-relaxed font-medium

/* Standard Body Text */
text-lg text-gray-300 leading-relaxed font-medium

/* Small Body Text */
text-sm text-gray-300 font-medium
```

### Special Text Styles
```css
/* Title Case Labels */
tracking-wide text-sm font-semibold

/* Service Subtitles */
text-sm font-semibold text-[#FEC400]
```

## Layout & Spacing

### Container Widths
- **Standard Container**: `max-w-7xl mx-auto`
- **Content Container**: `max-w-[1280px] mx-auto`
- **Text Container**: `max-w-4xl mx-auto`

### Section Padding
```css
/* Hero Sections */
pt-44 pb-24 px-6

/* Content Sections */
py-16 md:py-24 px-6

/* Compact Sections */
py-16 px-6
```

### Grid Layouts
```css
/* Service Cards */
grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8

/* Two Column Layout - Standard */
grid md:grid-cols-2 gap-12

/* Two Column Layout - Minimized */
grid md:grid-cols-2 gap-8

/* Feature Grid */
grid grid-cols-1 md:grid-cols-2 gap-6
```

### Minimized Spacing (For Cohesive Sections)
```css
/* Content Groups - Minimized */
space-y-4 (instead of space-y-8)

/* Card Padding - Minimized */
p-6 (instead of p-8)

/* Element Margins - Minimized */
mb-3 (instead of mb-4 or mb-6)

/* Grid Gaps - Minimized */
gap-8 (instead of gap-12)

/* Use for: Founder stories, detailed content sections, professional layouts */
```

## Component Styles

### Cards
```css
/* Standard Card */
bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/40 transition-all duration-300

/* Service Card with Hover */
bg-[#060d25]/80 backdrop-blur-sm rounded-2xl p-8 border border-[#6B8EF5]/20 hover:border-[#6B8EF5]/50 transition-all duration-300 hover:bg-[#060d25]/90
```

### Buttons
```css
/* Standard CTA Button - LOCKED DESIGN */
bg-black text-white border-2 border-[#FF1717] px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_20px_rgba(254,196,0,0.4)]

/* Legacy Button Styles (Deprecated) */
bg-[#6B8EF5] hover:bg-[#5B7FE8] text-white border-none px-8 py-3 text-base font-semibold rounded-full transition-all duration-500 hover:shadow-[0_0_25px_rgba(107,142,245,0.5)]
```

### Icons
```css
/* Standard Icon */
h-8 w-8 text-[#6B8EF5]

/* Icon Container */
bg-[#6B8EF5]/10 p-4 rounded-xl inline-block

/* Small Icon */
h-4 w-4 text-[#6B8EF5]
```

### Section Badges
```css
/* Section Identifier Badge */
inline-flex items-center gap-2 bg-[#6B8EF5]/10 text-[#6B8EF5] px-6 py-3 rounded-full text-sm font-medium border border-[#6B8EF5]/20

/* Warning/Alert Badge */
inline-flex items-center gap-2 bg-[#FF1717]/10 text-[#FF1717] px-6 py-3 rounded-full text-sm font-medium border border-[#FF1717]/30
```

## Animation Standards

### Page Load Animations
```javascript
// Standard fade-in animation for sections
initial={{ opacity: 0, y: 30 }}
whileInView={{ opacity: 1, y: 0 }}
viewport={{ once: true }}
transition={{ duration: 0.8 }}

// Staggered animations for cards
transition={{ duration: 0.6, delay: index * 0.1 }}
```

### Hover Effects
```css
/* Card Hover */
hover:border-[#6B8EF5]/40 transition-all duration-300

/* Button Hover */
hover:shadow-[0_0_25px_rgba(107,142,245,0.5)] transition-all duration-300

/* Scale Hover */
hover:scale-105 transition-all duration-300

/* Warning/Alert Hover */
hover:border-[#FF1717]/40 hover:bg-[#FF1717]/5 transition-all duration-300
```

## Background Effects

### Glow Effects
```css
/* Brand Red Glow (Hero Sections) - Subtle */
pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-[#FF1717]/15 rounded-full blur-3xl z-0

/* Brand Red Glow (Alert Sections) - Medium */
pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-[#FF1717]/25 rounded-full blur-3xl z-0
```

### Grid Patterns
```css
/* Royal Blue Grid Pattern */
backgroundImage: `
  linear-gradient(rgba(107, 142, 245, 0.1) 1px, transparent 1px),
  linear-gradient(90deg, rgba(107, 142, 245, 0.1) 1px, transparent 1px)
`,
backgroundSize: "50px 50px"
```

## Brand Red Integration Examples

### Alert Components
```css
/* Critical Alert */
bg-[#FF1717]/10 border border-[#FF1717]/30 text-[#FF1717] rounded-lg p-4

/* Warning State */
bg-[#FF1717]/5 border border-[#FF1717]/20 text-white rounded-lg p-4

/* Threat Emphasis Text */
text-[#FF1717] drop-shadow-[0_0_8px_rgba(255,23,23,0.3)] font-semibold

/* Subtle Threat Highlight */
text-[#FF1717]/80 font-semibold
```

### Usage Guidelines
```css
/* DO: Use for critical threats and warnings */
<span className="text-[#FF1717] drop-shadow-[0_0_8px_rgba(255,23,23,0.3)]">CRITICAL</span>

/* DO: Use for alert backgrounds */
<div className="bg-[#FF1717]/10 border border-[#FF1717]/30">Alert Content</div>

/* DON'T: Use for primary buttons */
/* DON'T: Use at full opacity with Royal Blue */
/* DON'T: Use for large background areas */
```

## Page Structure Standards

### Standard Page Layout
1. **Navbar** - Fixed navigation
2. **Hero Section** (optional) - Main page introduction with CTA
3. **Content Sections** - Main page content with section badges
4. **CTA Section** - Secondary call-to-action
5. **Footer** - Site footer

### Hero Section Pattern
```jsx
<section className="pt-44 pb-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25] relative overflow-hidden">
  {/* Red Glow Background Effect */}
  <div className="pointer-events-none absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[400px] bg-red-500/30 rounded-full blur-3xl z-0" />
  
  <div className="max-w-7xl mx-auto relative z-10">
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
      className="text-center mb-16"
    >
      <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
        Title with <span className="text-[#6B8EF5]">Accent</span>
      </h1>
      <p className="text-xl text-gray-300 max-w-4xl mx-auto font-medium leading-relaxed mb-8">
        Description text
      </p>
      <div className="flex justify-center">
        <LeadGenerationButton />
      </div>
    </motion.div>
  </div>
</section>
```

### Content Section Pattern
```jsx
<section className="py-16 md:py-24 px-6 bg-gradient-to-br from-[#060d25] via-[#04081a] to-[#060d25]">
  <div className="max-w-7xl mx-auto">
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
      className="text-center mb-20"
    >
      <div className="inline-flex items-center gap-2 bg-[#6B8EF5]/10 text-[#6B8EF5] px-6 py-3 rounded-full text-sm font-medium mb-8 border border-[#6B8EF5]/20">
        Section Badge
      </div>
      <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-wide">
        Section Title with <span className="text-[#6B8EF5]">Accent</span>
      </h2>
      <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-medium">
        Section description
      </p>
    </motion.div>

    {/* Content Grid */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {/* Cards */}
    </div>
  </div>
</section>
```

## Brand Guidelines

### Logo Usage
- Use Teclara logo with proper spacing
- Maintain logo integrity and proportions
- Logo should have adequate contrast against backgrounds

### Voice & Tone
- **Professional yet approachable**
- **Security-focused messaging**
- **Clear, direct communication**
- **Emphasis on business protection and growth**

### Content Patterns
- **Problem-focused headlines** (emphasize pain points)
- **Solution-oriented descriptions**
- **Benefit-driven bullet points**
- **Strong call-to-action language**

## Technical Implementation

### Required Dependencies
```json
{
  "framer-motion": "^10.x.x",
  "lucide-react": "^0.x.x",
  "tailwindcss": "^3.x.x"
}
```

### Tailwind Configuration
Ensure these colors are available in your Tailwind config:
```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        // Current Design System
        'teclara-navy': '#060d25',
        'teclara-navy-light': '#04081a',
        'teclara-royal-blue': '#6B8EF5',
        'teclara-royal-blue-hover': '#5B7FE8',
        'teclara-yellow': '#FEC400',
        'teclara-brand-red': '#FF1717',

        // Legacy Colors (Still in Use)
        'teclara-primary': '#D14019',
        'teclara-primary-hover': '#CC1414',
        'teclara-turquoise': '#34c5b6',

        // CSS Custom Properties (Active)
        '--background-primary': '#060d25',
        '--background-secondary': '#04081a',
        '--brand-primary': '#34c5b6',
        '--brand-secondary': '#fec400',
        '--text-primary': '#ffffff',
        '--text-secondary': '#9ca3af',
      }
    }
  }
}
```

## Quality Assurance Checklist

### Design Consistency
- [ ] Uses approved color palette
- [ ] Follows typography hierarchy
- [ ] Implements standard spacing
- [ ] Uses consistent component styles
- [ ] Includes proper animations

### Accessibility
- [ ] Sufficient color contrast ratios
- [ ] Proper heading hierarchy (H1 → H2 → H3)
- [ ] Alt text for images
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility

### Performance
- [ ] Optimized images and assets
- [ ] Efficient animation implementations
- [ ] Minimal layout shifts
- [ ] Fast loading times

### Brand Compliance
- [ ] Maintains professional tone
- [ ] Uses security-focused messaging
- [ ] Includes appropriate CTAs
- [ ] Follows content patterns

## Maintenance Notes

### Version Control
- This design system is locked as of December 2024
- Any changes require approval and documentation updates
- Maintain backward compatibility when possible

### Future Updates
- Document all changes in this file
- Update component library accordingly
- Test across all existing pages
- Maintain design consistency

---

**Last Updated**: December 2024
**Version**: 1.0
**Status**: LOCKED - Base Theme Established
