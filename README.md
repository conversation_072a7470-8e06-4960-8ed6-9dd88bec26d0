# Teclara Corporate Website

Enterprise-grade cybersecurity, managed IT, and business continuity solutions for modern businesses.

## Features

- **Performance**: Blazing fast with Vite, React 18, and optimized bundle splitting
- **Design System**: Locked Teclara design system with Royal Blue (#4169E1) and Brand Red (#FF1717)
- **UI/UX**: Modern interface with Tailwind CSS and Radix UI primitives
- **Responsive**: Fully responsive design optimized for all devices
- **SEO**: Comprehensive SEO with meta tags, structured data, and sitemap
- **Security**: Production-ready security headers and Content Security Policy
- **Analytics**: PostHog integration with Core Web Vitals monitoring
- **Forms**: Fillout integration for lead generation and contact forms
- **Accessibility**: WCAG 2.1 AA compliant with proper focus management

## Quick Start

1. Clone the repository

   ```bash
   git clone https://github.com/yourusername/teclara-corp-site.git
   cd teclara-corp-site
   ```

2. Install dependencies

   ```bash
   npm install
   ```

3. Start the development server

   ```bash
   npm run dev
   ```

   Open http://localhost:8080 to view it in the browser.

4. Build for production
   ```bash
   npm run build
   ```
   The build artifacts will be stored in the dist/ directory.

## Development Environment

### Gitpod.io (Recommended)

This project is optimized for Gitpod.io development environments with **Cursor-specific optimizations** to reduce cold-start time. Simply click the button below to start coding:

[![Open in Gitpod](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/#https://github.com/yourusername/teclara-corp-site)

The Gitpod environment includes:

- **Custom Dockerfile** with pre-installed tools (wget, curl, tar, openssh-client, git-lfs, jq, htop, tree)
- **Global Node.js tools** (yarn, pnpm, typescript, ts-node, nodemon, concurrently)
- **Pre-installed VS Code extensions** for faster startup
- **Experimental Cursor remote server prefetching** (see limitations below)
- **Optimized caching** for npm, yarn, and pnpm
- **Automatic dependency installation** and environment setup
- **Development server** on port 8080
- **GitHub integration** with prebuilds for faster workspace startup

#### Production Ready Features:

- **Code Quality**: TypeScript strict mode with comprehensive error checking
- **Performance**: Bundle optimization with tree-shaking and compression
- **Security**: CSP headers, HTTPS enforcement, and secure form handling
- **Monitoring**: Core Web Vitals tracking and performance monitoring

### Dev Container (Gitpod.io Managed)

This project includes a devcontainer configuration that's automatically managed by Gitpod.io:

- Ubuntu Jammy base image
- Node.js 20.x
- Git and GitHub CLI
- Port forwarding for the Vite dev server (8080)
- Optimized workspace mounting for better performance

The container environment is automatically set up when you open the project in Gitpod.io.

### Local Development

For traditional local development, ensure you have:

- Node.js 18+ installed
- npm or yarn package manager
- Git for version control

## Deploying to Cloudflare Pages

1. Install Wrangler CLI (if not already installed)

   ```bash
   npm install -g wrangler
   ```

2. Login to Cloudflare

   ```bash
   wrangler login
   ```

3. Deploy to Cloudflare Pages

   ```bash
   # For production
   wrangler pages deploy --project-name=teclara-corp-site --branch=main --env=production

   # For preview
   wrangler pages deploy --project-name=teclara-corp-site --branch=staging --env=preview
   ```

4. Configure custom domain (if needed)
   ```bash
   wrangler pages domain add teclara.tech
   ```

### Environment Variables

Set the following environment variables in your Cloudflare Pages project settings:

- NODE_VERSION: 18
- VITE_APP_ENV: production or staging
- VITE_APP_URL: Your production URL (e.g., https://teclara.tech)
- VITE_BUILD_NUMBER: Automatically set from git commit hash

## Project Structure

```
src/
├── components/       # Reusable UI components
├── pages/            # Page components
├── assets/           # Static assets (images, fonts, etc.)
├── styles/           # Global styles and Tailwind config
├── hooks/            # Custom React hooks
├── utils/            # Utility functions
├── types/            # TypeScript type definitions
└── App.tsx          # Main application component
```

## Configuration

### Environment Variables

Create a .env file in the root directory for local development:

```env
VITE_APP_ENV=development
VITE_BUILD_NUMBER=local-dev
```

### Build Configuration

- Production build: npm run build
- Development build: npm run dev
- Lint code: npm run lint
- Format code: npm run format

## Analytics & Monitoring

The application includes comprehensive analytics and monitoring:

- **PostHog**: User behavior analytics and feature flags
- **Core Web Vitals**: Performance monitoring with automatic reporting
- **Custom Events**: Lead generation and conversion tracking
- **Error Monitoring**: Production error tracking and reporting

## Contributing

1. Fork the repository
2. Create your feature branch (git checkout -b feature/AmazingFeature)
3. Commit your changes (git commit -m 'Add some AmazingFeature')
4. Push to the branch (git push origin feature/AmazingFeature)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [Vite](https://vitejs.dev/)
- [React](https://reactjs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Lucide Icons](https://lucide.dev/)

## Development Environment

### Gitpod.io (Recommended)

This project is optimized for Gitpod.io development environments with **Cursor-specific optimizations** to reduce cold-start time. Simply click the button below to start coding:

[![Open in Gitpod](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/#https://github.com/yourusername/teclara-corp-site)

The Gitpod environment includes:

- **Custom Dockerfile** with pre-installed tools (wget, curl, tar, openssh-client, git-lfs, jq, htop, tree)
- **Global Node.js tools** (yarn, pnpm, typescript, ts-node, nodemon, concurrently)
- **Pre-installed VS Code extensions** for faster startup
- **Experimental Cursor remote server prefetching** (see limitations below)
- **Optimized caching** for npm, yarn, and pnpm
- **Automatic dependency installation** and environment setup
- **Development server** on port 8080
- **GitHub integration** with prebuilds for faster workspace startup

#### Cursor Optimization Notes:

- **Experimental prefetching**: The workspace attempts to prefetch Cursor's remote server binary
- **Limitations**: Cursor doesn't yet support preloading like VS Code, and URLs are version-specific
- **Expected behavior**: Cursor will still download its own binary, but the workspace will be ready faster
- **Testing**: Run `./scripts/test-cursor-prefetch.sh` to test the prefetch functionality

For detailed information about the optimization, see [docs/GITPOD_OPTIMIZATION.md](docs/GITPOD_OPTIMIZATION.md).

### Dev Container (Gitpod.io Managed)

This project includes a devcontainer configuration that's automatically managed by Gitpod.io:

- Ubuntu Jammy base image
- Node.js 20.x
- Git and GitHub CLI
- Port forwarding for the Vite dev server (8080)
- Optimized workspace mounting for better performance

The container environment is automatically set up when you open the project in Gitpod.io.

### Local Development

For traditional local development, ensure you have:

- Node.js 18+ installed
- npm or yarn package manager
- Git for version control
