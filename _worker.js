// Cloudflare Worker for SPA routing
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  const path = url.pathname
  
  // Handle static assets with proper MIME types
  if (/\.[a-z0-9]+$/i.test(path)) {
    const response = await fetch(request)

    // Clone the response to modify headers
    const newResponse = new Response(response.body, response)

    // Set correct MIME types for JavaScript modules
    if (path.endsWith('.js') || path.endsWith('.mjs')) {
      newResponse.headers.set('Content-Type', 'text/javascript; charset=utf-8')
      newResponse.headers.set('X-Content-Type-Options', 'nosniff')
    } else if (path.endsWith('.jsx') || path.endsWith('.tsx')) {
      newResponse.headers.set('Content-Type', 'text/javascript; charset=utf-8')
      newResponse.headers.set('X-Content-Type-Options', 'nosniff')
    } else if (path.endsWith('.css')) {
      newResponse.headers.set('Content-Type', 'text/css; charset=utf-8')
      newResponse.headers.set('X-Content-Type-Options', 'nosniff')
    } else if (path.endsWith('.json')) {
      newResponse.headers.set('Content-Type', 'application/json; charset=utf-8')
      newResponse.headers.set('X-Content-Type-Options', 'nosniff')
    } else if (path.endsWith('.html')) {
      newResponse.headers.set('Content-Type', 'text/html; charset=utf-8')
      // Only apply Clear-Site-Data to HTML files
      newResponse.headers.set('Clear-Site-Data', '"cache", "cookies", "storage"')
      newResponse.headers.set('Cross-Origin-Embedder-Policy', 'credentialless')
    }

    return newResponse
  }

  // For all other requests, return the main index.html
  const response = await fetch('https://your-pages-url.pages.dev/index.html')
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: {
      ...Object.fromEntries(response.headers.entries()),
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://umami.is https://cloud.umami.is https://api-gateway.umami.dev https://bitdefender.com https://*.bitdefender.com https://static.cloudflareinsights.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com data:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://umami.is https://cloud.umami.is https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com; img-src 'self' data: https: blob:; font-src 'self' data: https://fonts.gstatic.com https://forms.fillout.com https://*.fillout.com; frame-src 'self' https://www.youtube.com https://www.google.com https://umami.is https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com; connect-src 'self' https://umami.is https://cloud.umami.is https://api-gateway.umami.dev https://bitdefender.com https://*.bitdefender.com https://static.cloudflareinsights.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com; media-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self' https://forms.fillout.com https://*.fillout.com; frame-ancestors 'none'; upgrade-insecure-requests;",
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
      'X-Permitted-Cross-Domain-Policies': 'none',
      'Cross-Origin-Embedder-Policy': 'credentialless',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'same-site'
    }
  })
}
