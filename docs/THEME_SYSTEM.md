# Teclara Technologies - Centralized Theme System

## Overview

This document describes the centralized color theme system implemented for Teclara Technologies. The system eliminates hardcoded color values and provides a consistent, maintainable approach to color management across the entire application.

## Architecture

### Core Components

1. **Theme Configuration** (`src/theme/colors.ts`)

   - Centralized color definitions
   - Brand guidelines compliance
   - Light/dark mode support
   - Semantic color system

2. **Theme Provider** (`src/theme/ThemeProvider.tsx`)

   - React context for theme management
   - Dynamic theme switching
   - CSS custom properties injection
   - Local storage persistence

3. **Tailwind Integration** (`tailwind.config.ts`)
   - Theme-aware utility classes
   - CSS custom property mapping
   - Extended color palette

## Color System Structure

### Brand Colors

Based on official brand guidelines:

```typescript
// Primary Brand Colors
primary: {
  red: '#D14019',           // Primary Red (main logo/icon color)
  crimson: '#CC1414',       // Crimson Depth (hover/emphasis variant)
  oxide: '#A31212',         // Oxide Red (deep/system variant)
  navy: '#14213D',          // Primary Navy (supporting accent)
  gunmetal: '#5C5F63',      // Gunmetal Gray (muted/passive/disabled)
}

// Background Colors
background: {
  light: '#F8F9FA',         // Light Mode Background
  dark: '#14213D',          // Dark Mode Background
}
```

### Semantic Colors

For UI states and feedback:

```typescript
success: { light: '#10B981', dark: '#059669', bg: '#ECFDF5', text: '#065F46' }
warning: { light: '#F59E0B', dark: '#D97706', bg: '#FFFBEB', text: '#92400E' }
error: { light: '#EF4444', dark: '#DC2626', bg: '#FEF2F2', text: '#991B1B' }
info: { light: '#3B82F6', dark: '#2563EB', bg: '#EFF6FF', text: '#1E40AF' }
```

### Contextual Colors

For cybersecurity and business contexts:

```typescript
// Threat Levels
threat: {
  critical: '#D14019',      // Critical threats (brand red)
  high: '#CC1414',          // High threats (crimson)
  medium: '#A31212',        // Medium threats (oxide red)
  low: '#5C5F63',           // Low threats (gunmetal)
}

// Security States
security: {
  secure: '#10B981',        // Secure state (green)
  scanning: '#F59E0B',      // Scanning state (amber)
  vulnerable: '#D14019',    // Vulnerable state (brand red)
  unknown: '#5C5F63',       // Unknown state (gunmetal)
}
```

## Usage Guidelines

### 1. Using Tailwind Classes

**✅ Recommended:**

```jsx
// Use theme-aware classes
<div className="bg-teclara-primary text-white">
<button className="bg-teclara-primary hover:bg-teclara-primary-hover">
<span className="text-threat-critical">Critical Alert</span>
```

**❌ Avoid:**

```jsx
// Don't use hardcoded colors
<div className="bg-[#D14019] text-white">
<button style={{ backgroundColor: '#D14019' }}>
```

### 2. Using Theme Hooks

```jsx
import {
  useTheme,
  useThemeColors,
  useBrandColors,
} from "@/theme/ThemeProvider";

function MyComponent() {
  const { theme, toggleTheme } = useTheme();
  const themeColors = useThemeColors();
  const brandColors = useBrandColors();

  return (
    <div style={{ backgroundColor: themeColors.background.primary }}>
      <button onClick={toggleTheme}>
        Switch to {theme === "light" ? "dark" : "light"} mode
      </button>
    </div>
  );
}
```

### 3. CSS Custom Properties

The theme system automatically generates CSS custom properties:

```css
/* Available in CSS */
.my-component {
  background-color: var(--background-primary);
  color: var(--text-primary);
  border-color: var(--brand-primary);
}
```

## Migration Guide

### From Hardcoded Colors

**Before:**

```jsx
<div className="bg-[#D14019] hover:bg-[#CC1414]">
  <span style={{ color: "#14213D" }}>Text</span>
</div>
```

**After:**

```jsx
<div className="bg-teclara-primary hover:bg-teclara-primary-hover">
  <span className="text-teclara-navy">Text</span>
</div>
```

### Common Replacements

| Hardcoded Color | Theme Class             | Usage                 |
| --------------- | ----------------------- | --------------------- |
| `#D14019`       | `teclara-primary`       | Primary brand color   |
| `#CC1414`       | `teclara-primary-hover` | Hover states          |
| `#14213D`       | `teclara-navy`          | Secondary brand color |
| `#1A1A1A`       | `gray-900`              | Dark backgrounds      |
| `#F8F9FA`       | `teclara-light-bg`      | Light backgrounds     |

## Theme Variants

### Light Mode

- Background: `#F8F9FA`
- Primary text: `#111827`
- Brand primary: `#D14019`
- Interactive elements: High contrast

### Dark Mode

- Background: `#14213D`
- Primary text: `#F9FAFB`
- Brand primary: `#D14019` (consistent)
- Interactive elements: Adapted contrast

## Best Practices

### 1. Consistency

- Always use theme colors instead of hardcoded values
- Maintain consistent color usage across components
- Follow semantic color meanings

### 2. Accessibility

- Ensure sufficient contrast ratios
- Test with both light and dark modes
- Consider color-blind users

### 3. Maintainability

- Use semantic names over descriptive names
- Group related colors logically
- Document color usage and meanings

### 4. Performance

- Leverage CSS custom properties for dynamic theming
- Minimize inline styles
- Use Tailwind utilities when possible

## Component Examples

### Button Component

```jsx
// Using theme-aware button variants
<Button variant="default">Primary Action</Button>
<Button variant="threat">Critical Action</Button>
<Button variant="secure">Safe Action</Button>
```

### Card Component

```jsx
// Theme-aware card styling
<Card className="bg-card border-border">
  <CardHeader className="text-foreground">
    <CardTitle className="text-brand-primary">Title</CardTitle>
  </CardHeader>
</Card>
```

## Extending the Theme

### Adding New Colors

1. **Update color definitions** in `src/theme/colors.ts`:

```typescript
export const brandColors = {
  // ... existing colors
  newColor: "#HEXCODE",
};
```

2. **Add to Tailwind config** in `tailwind.config.ts`:

```typescript
colors: {
  // ... existing colors
  'new-color': colors.brand.newColor,
}
```

3. **Update theme variants** if needed for light/dark mode support.

### Adding New Semantic Colors

```typescript
// In colors.ts
export const semanticColors = {
  // ... existing colors
  newSemantic: {
    light: "#HEXCODE",
    dark: "#HEXCODE",
    bg: "#HEXCODE",
    text: "#HEXCODE",
  },
};
```

## Testing

### Visual Testing

- Test all components in both light and dark modes
- Verify color consistency across pages
- Check accessibility contrast ratios

### Automated Testing

- Unit tests for theme provider functionality
- Integration tests for theme switching
- Visual regression tests for color changes

## Troubleshooting

### Common Issues

1. **Colors not updating**: Ensure ThemeProvider wraps your app
2. **CSS custom properties not working**: Check if theme is properly initialized
3. **Tailwind classes not found**: Verify color definitions in tailwind.config.ts

### Debug Tools

```jsx
// Debug current theme state
import { useTheme } from "@/theme/ThemeProvider";

function DebugTheme() {
  const { theme, colors } = useTheme();
  console.log("Current theme:", theme);
  console.log("Available colors:", colors);
}
```

## Future Enhancements

- [ ] High contrast mode support
- [ ] Custom theme creation interface
- [ ] Color palette generator
- [ ] Theme preview component
- [ ] Automated color accessibility testing

## Resources

- [Brand Guidelines](../brand-color-update.md)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [WCAG Color Contrast Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)
