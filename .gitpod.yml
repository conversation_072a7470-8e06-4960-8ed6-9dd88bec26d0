image:
  file: .gitpod.Dockerfile

tasks:
  - name: Setup Development Environment
    init: |
      # Run Cursor prefetch script in background
      /workspace/prefetch-cursor.sh &

      # Install project dependencies
      npm install

      # Set up git configuration if not already set
      git config --global pull.rebase false || true

      # Create useful aliases
      echo 'alias ll="ls -la"' >> ~/.bashrc
      echo 'alias ll="ls -la"' >> ~/.zshrc
      echo 'alias gs="git status"' >> ~/.bashrc
      echo 'alias gs="git status"' >> ~/.zshrc

      # Optimize npm/yarn for faster installs
      npm config set cache /workspace/.npm-cache
      npm config set prefer-offline true

      echo "🚀 Environment setup completed!"
    command: npm run dev

ports:
  - port: 8080
    onOpen: open-preview
    visibility: public
  - port: 3000
    onOpen: open-preview
    visibility: public

vscode:
  extensions:
    # Essential extensions for JavaScript/TypeScript development
    - esbenp.prettier-vscode
    - bradlc.vscode-tailwindcss
    - ms-vscode.vscode-typescript-next
    - dbaeumer.vscode-eslint
    - ms-vscode.vscode-json
    - ms-vscode.vscode-css-peek
    - ms-vscode.vscode-html-css-support

    # Git and GitHub
    - eamodio.gitlens
    - github.vscode-pull-request-github

    # Productivity
    - christian-kohler.path-intellisense
    - ms-vscode.vscode-json
    - bradlc.vscode-tailwindcss
    - ms-vscode.vscode-css-peek
    - ms-vscode.vscode-html-css-support

     # React development
    - ms-vscode.vscode-react-native

    # Additional useful extensions
    - ms-vscode.vscode-docker
    - redhat.vscode-yaml
    - ms-vscode.vscode-markdown-all-in-one
    - shan.code-settings-sync

# Environment variables for better performance
env:
  NODE_ENV: development
  NPM_CONFIG_CACHE: /workspace/.npm-cache
  YARN_CACHE_FOLDER: /workspace/.yarn-cache
  PNPM_STORE_DIR: /workspace/.pnpm-store

# Workspace optimizations
workspaceLocation: "."
