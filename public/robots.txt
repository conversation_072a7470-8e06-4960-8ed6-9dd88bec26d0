# Teclara Technologies robots.txt
# Last updated: 2025-06-19

# Allow all crawlers by default
User-agent: *
Allow: /

# Sitemap location
Sitemap: https://teclara.tech/sitemap.xml

# Disallow admin and private areas
Disallow: /admin/
Disallow: /private/
Disallow: /api/

# Block AI training crawlers
User-agent: GPTBot
Disallow: /

User-agent: ChatGPT-User
Disallow: /

User-agent: Google-Extended
Disallow: /

User-agent: CCBot
Disallow: /

User-agent: anthropic-ai
Disallow: /

User-agent: Claude-Web
Disallow: /

# Disallow thank you and error pages
Disallow: /thank-you
Disallow: /404
Disallow: /error

# Disallow query strings and session IDs
Disallow: /*?*

# Clean parameters for better crawling
Clean-param: utm_source&utm_medium&utm_campaign&utm_content&utm_term&fbclid&gclid /

# Google-specific directives
User-agent: Googlebot
Allow: /*.css
Allow: /*.js
Allow: /*.svg
Allow: /*.png
Allow: /*.jpg
Allow: /*.jpeg
Allow: /*.gif
Allow: /*.webp
Allow: /*.woff
Allow: /*.woff2
Allow: /*.ttf
Allow: /*.eot

# Bing-specific directives
User-agent: bingbot
Crawl-delay: 5
Allow: /

# Block aggressive crawlers
User-agent: AhrefsBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: dotbot
Disallow: /

User-agent: rogerbot
Disallow: /

# Allow social media bots
User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

User-agent: facebookexternalhit
Allow: /

# Allow search engine image bots
User-agent: Googlebot-Image
Allow: /

# Allow DuckDuckGo
User-agent: DuckDuckBot
Allow: /

# Allow Apple bot
User-agent: Applebot
Allow: /
