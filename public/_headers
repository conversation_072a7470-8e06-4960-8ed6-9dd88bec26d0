# Security Headers for HTML pages only
/*.html
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(self), microphone=(self), geolocation=()
  Strict-Transport-Security: max-age=********; includeSubDomains; preload
  X-Permitted-Cross-Domain-Policies: none
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: cross-origin

  Cross-Origin-Embedder-Policy: credentialless
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://www.clarity.ms https://static.cloudflareinsights.com data:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com; img-src 'self' data: https: blob:; font-src 'self' data: https://fonts.gstatic.com https://forms.fillout.com https://*.fillout.com; frame-src 'self' https://www.youtube.com https://www.google.com https://bitdefender.com https://*.bitdefender.com https://threatmap.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com; connect-src 'self' https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com https://www.clarity.ms https://*.clarity.ms https://cloudflareinsights.com https://*.cloudflareinsights.com; media-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self' https://forms.fillout.com https://*.fillout.com; frame-ancestors 'none'; upgrade-insecure-requests;
  X-Robots-Tag: index, follow
  X-DNS-Prefetch-Control: on
  Expect-CT: max-age=86400, enforce

# Root index.html (SPA routing)
/
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(self), microphone=(self), geolocation=()
  Strict-Transport-Security: max-age=********; includeSubDomains; preload
  X-Permitted-Cross-Domain-Policies: none
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: cross-origin

  Cross-Origin-Embedder-Policy: credentialless
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://www.clarity.ms https://static.cloudflareinsights.com data:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com; img-src 'self' data: https: blob:; font-src 'self' data: https://fonts.gstatic.com https://forms.fillout.com https://*.fillout.com; frame-src 'self' https://www.youtube.com https://www.google.com https://bitdefender.com https://*.bitdefender.com https://threatmap.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com; connect-src 'self' https://bitdefender.com https://*.bitdefender.com https://*.teclara.tech https://forms.fillout.com https://*.fillout.com https://outlook.office.com https://www.clarity.ms https://*.clarity.ms https://cloudflareinsights.com https://*.cloudflareinsights.com; media-src 'self' https:; object-src 'none'; base-uri 'self'; form-action 'self' https://forms.fillout.com https://*.fillout.com; frame-ancestors 'none'; upgrade-insecure-requests;
  X-Robots-Tag: index, follow
  X-DNS-Prefetch-Control: on
  Expect-CT: max-age=86400, enforce

# Cache control for static assets
/assets/*
  Cache-Control: public, max-age=********, immutable

# Cache control for HTML files
/*.html
  Cache-Control: public, max-age=0, must-revalidate

# Cache control for API responses
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  X-Content-Type-Options: nosniff

# MIME types for JavaScript modules
/assets/js/*.js
  Content-Type: text/javascript; charset=utf-8
  X-Content-Type-Options: nosniff

# MIME types for CSS files
/assets/css/*.css
  Content-Type: text/css; charset=utf-8
  X-Content-Type-Options: nosniff

# MIME types for all JavaScript files
*.js
  Content-Type: text/javascript; charset=utf-8
  X-Content-Type-Options: nosniff

# MIME types for all CSS files
*.css
  Content-Type: text/css; charset=utf-8
  X-Content-Type-Options: nosniff
