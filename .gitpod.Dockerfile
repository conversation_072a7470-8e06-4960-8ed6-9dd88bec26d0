FROM gitpod/workspace-full:latest

# Install additional system tools for better development experience
RUN sudo apt-get update && sudo apt-get install -y \
    wget \
    curl \
    tar \
    openssh-client \
    git-lfs \
    jq \
    htop \
    tree \
    unzip \
    zip \
    && sudo rm -rf /var/lib/apt/lists/*

# Install global Node.js tools
RUN npm install -g \
    yarn \
    pnpm \
    npm-check-updates \
    typescript \
    ts-node \
    nodemon \
    concurrently \
    cross-env \
    rimraf \
    && npm cache clean --force

# Install additional development tools
RUN sudo apt-get update && sudo apt-get install -y \
    build-essential \
    python3 \
    python3-pip \
    && sudo rm -rf /var/lib/apt/lists/*

# Install Python tools
RUN pip3 install --user \
    black \
    flake8 \
    mypy \
    pre-commit

# Set up workspace optimizations
RUN mkdir -p /workspace/.cursor-cache

# Create a script to attempt prefetching Cursor remote server
RUN echo '#!/bin/bash\n\
# Attempt to prefetch Cursor remote server binary\n\
# Note: This is experimental and may not work due to version-specific URLs\n\
CURSOR_CACHE_DIR="/workspace/.cursor-cache"\n\
mkdir -p "$CURSOR_CACHE_DIR"\n\
\n\
# Common Cursor remote server URLs (these may change)\n\
CURSOR_URLS=(\n\
    "https://downloads.cursor.sh/linux-x64/cursor-server"\n\
    "https://downloads.cursor.sh/linux-x64/cursor-server-linux-x64"\n\
)\n\
\n\
echo "Attempting to prefetch Cursor remote server..."\n\
for url in "${CURSOR_URLS[@]}"; do\n\
    echo "Trying: $url"\n\
    if wget --spider "$url" 2>/dev/null; then\n\
        echo "Found valid URL: $url"\n\
        wget -O "$CURSOR_CACHE_DIR/cursor-server" "$url" 2>/dev/null || true\n\
        chmod +x "$CURSOR_CACHE_DIR/cursor-server" 2>/dev/null || true\n\
        break\n\
    fi\n\
done\n\
\n\
echo "Cursor prefetch attempt completed (may not be used by Cursor yet)"\n\
' > /workspace/prefetch-cursor.sh && chmod +x /workspace/prefetch-cursor.sh

# Set up environment variables for better performance
ENV NODE_ENV=development
ENV NPM_CONFIG_CACHE=/workspace/.npm-cache
ENV YARN_CACHE_FOLDER=/workspace/.yarn-cache
ENV PNPM_STORE_DIR=/workspace/.pnpm-store

# Create cache directories
RUN mkdir -p /workspace/.npm-cache /workspace/.yarn-cache /workspace/.pnpm-store

# Optimize shell configuration
RUN echo 'export PATH="$HOME/.local/bin:$PATH"' >> /home/<USER>/.bashrc && \
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> /home/<USER>/.zshrc

# Set up workspace permissions
RUN sudo chown -R gitpod:gitpod /workspace 